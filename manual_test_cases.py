"""
Manual Test Cases for IB Options Trading POC

Instructions:
1. Start TWS or IB Gateway
2. Run main.py
3. Copy and paste these test cases into the terminal when prompted
"""

# Test Case 1: Basic Connection and Account Info
# When prompted with the menu, enter: 1
# Expected: Should display account balances

# Test Case 2: Get Option Chain
# When prompted with the menu, enter: 3
# When asked for symbol, enter: SPY
# Expected: Should display available expirations and strikes for SPY options

# Test Case 3: Get Market Data with Delayed Data
# When prompted with the menu, enter: 4
# When asked for symbol, enter: SPY
# When asked for expiration, select one from the list (e.g., 1)
# When asked for strike, select one from the list (e.g., 10)
# When asked for option type, enter: C
# Expected: Should display market data for the selected option (may show zeros if no subscription)

# Test Case 4: Place a Test Order (Far OTM, small quantity)
# When prompted with the menu, enter: 6
# When asked for symbol, enter: SPY
# When asked for expiration, select one from the list (e.g., 1)
# When asked for strike, select one that's far OTM (e.g., if SPY is at 450, choose 400 for puts)
# When asked for option type, enter: P
# When asked for quantity, enter: 1
# When asked for limit price, enter a very low price: 0.01
# Expected: Should submit the order and display the order ID

# Test Case 5: Check Order Status
# When prompted with the menu, enter: 7
# When asked for order ID, enter the ID from Test Case 4
# Expected: Should display the status of your order

# Test Case 6: Cancel Order
# When prompted with the menu, enter: 8
# When asked for order ID, enter the ID from Test Case 4
# Expected: Should cancel the order and confirm cancellation

# Test Case 7: Check Market Data Permissions
# When prompted with the menu, enter: 9 (if available, otherwise use the appropriate menu option)
# Expected: Should display your market data permissions

# Test Case 8: Exit Application
# When prompted with the menu, enter: 10 (or the exit option number)
# Expected: Application should exit cleanly