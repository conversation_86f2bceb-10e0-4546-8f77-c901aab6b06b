import logging
import sys
from datetime import datetime

def setup_logger():
    """Configure and return a logger for the application"""
    logger = logging.getLogger('ClientPortal')
    logger.setLevel(logging.DEBUG)
    fh = logging.FileHandler(f'logs/client_portal_{datetime.now().strftime("%Y%m%d")}.log')
    fh.setLevel(logging.DEBUG)
    
    ch = logging.StreamHandler(sys.stdout)
    ch.setLevel(logging.INFO)
 
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)
    
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    return logger