from ib_insync import Option, MarketOrder, LimitOrder
from ib_options.connect import setup_logger

def create_option_contract(symbol, expiry, strike, right, exchange='SMART'):
    """Create an option contract
    
    Args:
        symbol: Underlying symbol (e.g., 'SPY')
        expiry: Expiration date in format YYYYMMDD
        strike: Strike price
        right: 'C' for Call, 'P' for Put
        exchange: Exchange (default 'SMART')
    
    Returns:
        Option contract object
    """
    return Option(symbol, expiry, strike, right, exchange, tradingClass=symbol)

def place_option_order(ib, contract, action, quantity, order_type='MKT', limit_price=None):
    """Place an option order
    
    Args:
        ib: IB connection object
        contract: Option contract
        action: 'BUY' or 'SELL'
        quantity: Number of contracts
        order_type: 'MKT' for market, 'LMT' for limit
        limit_price: Price for limit orders
    
    Returns:
        Trade object or None if error occurs
    """
    logger = setup_logger()
    
    try:
        # Qualify the contract
        qualified_contracts = ib.qualifyContracts(contract)
        if not qualified_contracts:
            logger.error(f"Could not qualify option contract: {contract.symbol} {contract.lastTradeDateOrContractMonth} {contract.strike} {contract.right}")
            return None
            
        contract = qualified_contracts[0]
        
        # Create order object
        if order_type == 'MKT':
            order = MarketOrder(action, quantity)
        elif order_type == 'LMT':
            if limit_price is None:
                raise ValueError("Limit price must be provided for limit orders")
            order = LimitOrder(action, quantity, limit_price)
        else:
            raise ValueError(f"Unsupported order type: {order_type}")
        
        # Submit the order
        trade = ib.placeOrder(contract, order)
        
        logger.info(f"Placed {order_type} order: {action} {quantity} {contract.symbol} {contract.right} {contract.strike} {contract.lastTradeDateOrContractMonth}")
        return trade
    except Exception as e:
        logger.error(f"Error placing option order: {str(e)}")
        return None

def get_order_status(ib, trade=None):
    """Get status of orders
    
    Args:
        ib: IB connection object
        trade: Specific trade to check (if None, get all open orders)
    
    Returns:
        Dictionary or list of dictionaries with order status or None if error occurs
    """
    logger = setup_logger()
    
    try:
        if trade:
            # Update specific trade
            ib.sleep(0.1)  # Small delay to ensure order status is updated
            status = {
                'orderId': trade.order.orderId,
                'status': trade.orderStatus.status,
                'filled': trade.orderStatus.filled,
                'remaining': trade.orderStatus.remaining,
                'avgFillPrice': trade.orderStatus.avgFillPrice,
                'lastFillPrice': trade.orderStatus.lastFillPrice,
                'whyHeld': trade.orderStatus.whyHeld
            }
            return status
        else:
            # Get all open orders
            open_trades = ib.openTrades()
            statuses = []
            
            for trade in open_trades:
                statuses.append({
                    'orderId': trade.order.orderId,
                    'status': trade.orderStatus.status,
                    'filled': trade.orderStatus.filled,
                    'remaining': trade.orderStatus.remaining,
                    'avgFillPrice': trade.orderStatus.avgFillPrice,
                    'lastFillPrice': trade.orderStatus.lastFillPrice,
                    'whyHeld': trade.orderStatus.whyHeld
                })
            
            logger.info(f"Retrieved status for {len(statuses)} orders")
            return statuses
    except Exception as e:
        logger.error(f"Error getting order status: {str(e)}")
        return None

def cancel_order(ib, trade):
    """Cancel an existing order
    
    Args:
        ib: IB connection object
        trade: Trade object to cancel
        
    Returns:
        True if successful, False otherwise
    """
    logger = setup_logger()
    
    try:
        ib.cancelOrder(trade.order)
        logger.info(f"Cancelled order {trade.order.orderId}")
        return True
    except Exception as e:
        logger.error(f"Error cancelling order: {str(e)}")
        return False
