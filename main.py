#!/usr/bin/env python3
"""
IB Options Trading POC - Professional Edition
Main entry point for the application

This is a comprehensive Python POC for Options trading with Interactive Brokers
that meets all requirements for Phase 1 of the GoTrade integration project.

Features:
- Connect to Interactive Brokers API using paper trading account
- Fetch current account balances relevant to Options trading
- Fetch current open Options positions (SPY Calls/Puts for specific strike and expiry)
- Fetch available symbols for the asset class (option chains for underlying symbols)
- Place new single-leg Options orders (market orders, limit orders for specific option contracts)
- Fetch the status of existing Options orders
- Enhanced error handling, logging, and performance monitoring
- Professional architecture ready for C++ implementation and GoTrade integration

Authors: <AUTHORS>
Version: 1.0.0
"""

import sys
import os
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ib_options.app import IBOptionsApp
from ib_options.config import config
from ib_options.connect import setup_logger
from ib_options.metrics import performance_monitor, export_performance_metrics

def setup_environment():
    """Setup the application environment"""
    # Ensure required directories exist
    config.ensure_directories()
    
    # Setup logging
    logger = setup_logger('Main')
    logger.info("IB Options Trading POC - Professional Edition")
    logger.info("=" * 60)
    logger.info("Phase 1: Python POC Development")
    logger.info("Objective: Develop a functional Python POC capable of all required functionality")
    logger.info("Core Tasks: API Connection & Authentication, Account Balances, Positions,")
    logger.info("           Option Chains, Market & Limit Order Placement, Order Status Tracking")
    logger.info("=" * 60)
    
    return logger

def print_banner():
    """Print application banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    IB Options Trading POC - Professional Edition            ║
║                                                                              ║
║  Phase 1: Python POC Development for GoTrade Integration                    ║
║                                                                              ║
║  Features:                                                                   ║
║  ✓ Interactive Brokers API Connection (Paper Trading)                       ║
║  ✓ Account Balances & Option Positions                                      ║
║  ✓ Option Chain Data & Market Data                                          ║
║  ✓ Market & Limit Order Placement                                           ║
║  ✓ Order Status Tracking & Management                                       ║
║  ✓ Professional Error Handling & Logging                                    ║
║  ✓ Performance Monitoring & Metrics                                         ║
║  ✓ Comprehensive Testing Suite                                              ║
║                                                                              ║
║  Ready for Phase 2: C++ Implementation                                      ║
║  Ready for Phase 3: GoTrade Product Integration                             ║
║                                                                              ║
║  Authors: <AUTHORS>
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def create_sample_env_file():
    """Create a sample .env configuration file"""
    sample_env = """# IB Options Trading POC Configuration
# Copy this file to .env and modify as needed

# Interactive Brokers Connection
IB_HOST=127.0.0.1
IB_PORT=7497
IB_CLIENT_ID=1
IB_TIMEOUT=30
IB_MAX_RETRIES=3
IB_RETRY_DELAY=1.0

# Trading Configuration
TRADING_EXCHANGE=SMART
TRADING_CURRENCY=USD
TRADING_MAX_ORDER_QTY=1000
TRADING_ORDER_TIMEOUT=300
TRADING_PAPER_MODE=true
TRADING_RISK_CHECK=true

# Market Data Configuration
MARKET_DATA_DELAYED=true
MARKET_DATA_TIMEOUT=10
MARKET_DATA_MAX_REQUESTS=50
MARKET_DATA_CACHE_DURATION=60
MARKET_DATA_GREEKS=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_FILE_PREFIX=ib_options
LOG_MAX_FILES=30
LOG_CONSOLE=true
LOG_FILE=true

# Performance Configuration
PERF_METRICS=true
PERF_METRICS_INTERVAL=60
PERF_BENCHMARK=false
PERF_MAX_MEMORY_MB=512
PERF_PROFILING=false
"""
    
    env_file = Path(".env.sample")
    with open(env_file, 'w') as f:
        f.write(sample_env)
    
    print(f"Sample configuration file created: {env_file}")
    print("Copy this to .env and modify as needed")

def run_tests():
    """Run the test suite"""
    try:
        import pytest
        print("Running test suite...")
        exit_code = pytest.main([
            "tests/",
            "-v",
            "--tb=short",
            "--disable-warnings"
        ])
        return exit_code
    except ImportError:
        print("pytest not installed. Install with: pip install pytest")
        return 1

def run_benchmarks():
    """Run performance benchmarks"""
    try:
        from benchmark import LatencyBenchmark
        print("Running performance benchmarks...")
        benchmark = LatencyBenchmark()
        benchmark.run_benchmarks()
        return 0
    except Exception as e:
        print(f"Error running benchmarks: {e}")
        return 1

def export_metrics():
    """Export performance metrics"""
    try:
        filename = export_performance_metrics()
        print(f"Performance metrics exported to: {filename}")
        return 0
    except Exception as e:
        print(f"Error exporting metrics: {e}")
        return 1

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="IB Options Trading POC - Professional Edition",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Run interactive application
  python main.py --test             # Run test suite
  python main.py --benchmark        # Run performance benchmarks
  python main.py --export-metrics   # Export performance metrics
  python main.py --create-config    # Create sample configuration file
  python main.py --version          # Show version information
        """
    )
    
    parser.add_argument(
        "--test", 
        action="store_true", 
        help="Run the test suite"
    )
    
    parser.add_argument(
        "--benchmark", 
        action="store_true", 
        help="Run performance benchmarks"
    )
    
    parser.add_argument(
        "--export-metrics", 
        action="store_true", 
        help="Export performance metrics"
    )
    
    parser.add_argument(
        "--create-config", 
        action="store_true", 
        help="Create sample configuration file"
    )
    
    parser.add_argument(
        "--version", 
        action="store_true", 
        help="Show version information"
    )
    
    parser.add_argument(
        "--no-banner", 
        action="store_true", 
        help="Skip banner display"
    )
    
    args = parser.parse_args()
    
    # Handle special commands
    if args.version:
        print("IB Options Trading POC - Professional Edition v1.0.0")
        print("Phase 1: Python POC Development")
        print("Authors: <AUTHORS>
        return 0
    
    if args.create_config:
        create_sample_env_file()
        return 0
    
    if args.test:
        return run_tests()
    
    if args.benchmark:
        return run_benchmarks()
    
    if args.export_metrics:
        return export_metrics()
    
    # Setup environment
    logger = setup_environment()
    
    # Display banner
    if not args.no_banner:
        print_banner()
    
    try:
        # Create and run the application
        app = IBOptionsApp()
        exit_code = app.run_interactive()
        
        # Export metrics on shutdown if enabled
        if config.performance.enable_metrics:
            try:
                metrics_file = export_performance_metrics()
                logger.info(f"Performance metrics exported to: {metrics_file}")
            except Exception as e:
                logger.warning(f"Failed to export metrics: {e}")
        
        return exit_code
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        return 1
    finally:
        # Stop performance monitoring
        performance_monitor.stop_monitoring()

if __name__ == "__main__":
    sys.exit(main())
