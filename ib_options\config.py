"""
Configuration management for IB Options Trading POC
"""

import os
from dataclasses import dataclass
from typing import Optional, Dict, Any
from pathlib import Path

@dataclass
class IBConnectionConfig:
    """Interactive Brokers connection configuration"""
    host: str = "127.0.0.1"
    port: int = 7497  # Paper trading port
    client_id: int = 1
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0

@dataclass
class TradingConfig:
    """Trading-specific configuration"""
    default_exchange: str = "SMART"
    default_currency: str = "USD"
    max_order_quantity: int = 1000
    default_order_timeout: int = 300  # seconds
    enable_paper_trading: bool = True
    risk_check_enabled: bool = True

@dataclass
class MarketDataConfig:
    """Market data configuration"""
    use_delayed_data: bool = True
    market_data_timeout: int = 10  # seconds
    max_concurrent_requests: int = 50
    cache_duration: int = 60  # seconds
    enable_greeks: bool = True

@dataclass
class LoggingConfig:
    """Logging configuration"""
    log_level: str = "INFO"
    log_dir: str = "logs"
    log_file_prefix: str = "ib_options"
    max_log_files: int = 30
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    enable_console_logging: bool = True
    enable_file_logging: bool = True

@dataclass
class PerformanceConfig:
    """Performance and monitoring configuration"""
    enable_metrics: bool = True
    metrics_interval: int = 60  # seconds
    enable_benchmarking: bool = False
    max_memory_usage_mb: int = 512
    enable_profiling: bool = False

class Config:
    """Main configuration class"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or os.getenv("IB_CONFIG_FILE", ".env")
        self.load_config()
    
    def load_config(self):
        """Load configuration from environment variables and config file"""
        # Load from .env file if it exists
        if os.path.exists(self.config_file):
            try:
                from dotenv import load_dotenv
                load_dotenv(self.config_file)
            except ImportError:
                pass  # dotenv not installed, skip
        
        # Initialize configurations
        self.ib_connection = IBConnectionConfig(
            host=os.getenv("IB_HOST", "127.0.0.1"),
            port=int(os.getenv("IB_PORT", "7497")),
            client_id=int(os.getenv("IB_CLIENT_ID", "1")),
            timeout=int(os.getenv("IB_TIMEOUT", "30")),
            max_retries=int(os.getenv("IB_MAX_RETRIES", "3")),
            retry_delay=float(os.getenv("IB_RETRY_DELAY", "1.0"))
        )
        
        self.trading = TradingConfig(
            default_exchange=os.getenv("TRADING_EXCHANGE", "SMART"),
            default_currency=os.getenv("TRADING_CURRENCY", "USD"),
            max_order_quantity=int(os.getenv("TRADING_MAX_ORDER_QTY", "1000")),
            default_order_timeout=int(os.getenv("TRADING_ORDER_TIMEOUT", "300")),
            enable_paper_trading=os.getenv("TRADING_PAPER_MODE", "true").lower() == "true",
            risk_check_enabled=os.getenv("TRADING_RISK_CHECK", "true").lower() == "true"
        )
        
        self.market_data = MarketDataConfig(
            use_delayed_data=os.getenv("MARKET_DATA_DELAYED", "true").lower() == "true",
            market_data_timeout=int(os.getenv("MARKET_DATA_TIMEOUT", "10")),
            max_concurrent_requests=int(os.getenv("MARKET_DATA_MAX_REQUESTS", "50")),
            cache_duration=int(os.getenv("MARKET_DATA_CACHE_DURATION", "60")),
            enable_greeks=os.getenv("MARKET_DATA_GREEKS", "true").lower() == "true"
        )
        
        self.logging = LoggingConfig(
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_dir=os.getenv("LOG_DIR", "logs"),
            log_file_prefix=os.getenv("LOG_FILE_PREFIX", "ib_options"),
            max_log_files=int(os.getenv("LOG_MAX_FILES", "30")),
            log_format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            enable_console_logging=os.getenv("LOG_CONSOLE", "true").lower() == "true",
            enable_file_logging=os.getenv("LOG_FILE", "true").lower() == "true"
        )
        
        self.performance = PerformanceConfig(
            enable_metrics=os.getenv("PERF_METRICS", "true").lower() == "true",
            metrics_interval=int(os.getenv("PERF_METRICS_INTERVAL", "60")),
            enable_benchmarking=os.getenv("PERF_BENCHMARK", "false").lower() == "true",
            max_memory_usage_mb=int(os.getenv("PERF_MAX_MEMORY_MB", "512")),
            enable_profiling=os.getenv("PERF_PROFILING", "false").lower() == "true"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "ib_connection": self.ib_connection.__dict__,
            "trading": self.trading.__dict__,
            "market_data": self.market_data.__dict__,
            "logging": self.logging.__dict__,
            "performance": self.performance.__dict__
        }
    
    def validate(self) -> bool:
        """Validate configuration"""
        errors = []
        
        # Validate IB connection
        if not (1 <= self.ib_connection.port <= 65535):
            errors.append(f"Invalid port: {self.ib_connection.port}")
        
        if not (1 <= self.ib_connection.client_id <= 32):
            errors.append(f"Invalid client_id: {self.ib_connection.client_id}")
        
        # Validate trading config
        if self.trading.max_order_quantity <= 0:
            errors.append(f"Invalid max_order_quantity: {self.trading.max_order_quantity}")
        
        # Validate logging config
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.logging.log_level not in valid_log_levels:
            errors.append(f"Invalid log_level: {self.logging.log_level}")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
        
        return True
    
    def ensure_directories(self):
        """Ensure required directories exist"""
        Path(self.logging.log_dir).mkdir(parents=True, exist_ok=True)

# Global configuration instance
config = Config()
