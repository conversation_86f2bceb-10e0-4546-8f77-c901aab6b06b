#!/usr/bin/env python3
"""
Basic Usage Examples for IB Options Trading POC

This script demonstrates all the core functionality of the POC:
1. Connecting to Interactive Brokers
2. Fetching account balances
3. Getting option positions
4. Retrieving option chains
5. Getting market data
6. Placing orders
7. Managing orders

Make sure TWS/Gateway is running on port 7497 (paper trading) before running this script.
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from ib_options import (
    IBOptionsClient, OptionContract, Order, OptionRight, 
    OrderType, OrderAction, config, setup_logger
)

def main():
    """Main example function"""
    logger = setup_logger('BasicUsage')
    
    print("="*60)
    print("IB Options Trading POC - Basic Usage Examples")
    print("="*60)
    
    # Create client
    client = IBOptionsClient()
    
    try:
        # 1. Connect to Interactive Brokers
        print("\n1. Connecting to Interactive Brokers...")
        if not client.connect():
            print("❌ Failed to connect to IB. Make sure TWS/Gateway is running on port 7497")
            return 1
        
        print("✅ Connected successfully!")
        
        # 2. Get account balances
        print("\n2. Fetching account balances...")
        balances = client.get_account_balances()
        
        if balances:
            print("✅ Account balances retrieved:")
            print(f"   Available Funds: ${balances.available_funds:,.2f}")
            print(f"   Buying Power: ${balances.buying_power:,.2f}")
            print(f"   Net Liquidation: ${balances.net_liquidation:,.2f}")
            print(f"   Option Market Value: ${balances.option_market_value:,.2f}")
        else:
            print("❌ Failed to retrieve account balances")
        
        # 3. Get option positions
        print("\n3. Fetching open option positions...")
        positions = client.get_option_positions()
        
        if positions:
            print(f"✅ Found {len(positions)} option positions:")
            for i, pos in enumerate(positions[:3], 1):  # Show first 3
                contract = pos.contract
                print(f"   {i}. {contract.symbol} {contract.right.value} ${contract.strike} {contract.expiry}")
                print(f"      Position: {pos.position} contracts, Avg Cost: ${pos.avg_cost:.2f}")
        else:
            print("ℹ️  No open option positions found")
        
        # 4. Get option chain for SPY
        print("\n4. Fetching option chain for SPY...")
        chain = client.get_option_chain("SPY")
        
        if chain:
            print("✅ SPY option chain retrieved:")
            print(f"   Underlying Price: ${chain.underlying_price:.2f}")
            print(f"   Expirations: {len(chain.expirations)} available")
            print(f"   Strikes: {len(chain.strikes)} available")
            print(f"   First 5 expirations: {', '.join(chain.expirations[:5])}")
            
            # Use the chain data for next examples
            nearest_expiry = chain.expirations[0]
            atm_strike = min(chain.strikes, key=lambda x: abs(x - chain.underlying_price))
            
        else:
            print("❌ Failed to retrieve SPY option chain")
            # Use default values
            nearest_expiry = "20241220"
            atm_strike = 450.0
        
        # 5. Get market data for a specific option
        print("\n5. Fetching market data for SPY option...")
        contract = OptionContract(
            symbol="SPY",
            expiry=nearest_expiry,
            strike=atm_strike,
            right=OptionRight.CALL
        )
        
        market_data = client.get_option_market_data(contract)
        
        if market_data:
            print("✅ Market data retrieved:")
            print(f"   Contract: SPY {contract.right.value} ${contract.strike} {contract.expiry}")
            print(f"   Bid: ${market_data.bid:.2f}")
            print(f"   Ask: ${market_data.ask:.2f}")
            print(f"   Mid: ${market_data.mid_price:.2f}")
            print(f"   Last: ${market_data.last:.2f}")
            print(f"   Volume: {market_data.volume:,}")
            print(f"   Open Interest: {market_data.open_interest:,}")
            print(f"   Implied Volatility: {market_data.implied_volatility:.4f}")
            
            if config.market_data.enable_greeks:
                print(f"   Greeks - Delta: {market_data.delta:.4f}, Gamma: {market_data.gamma:.4f}")
        else:
            print("❌ Failed to retrieve market data")
        
        # 6. Place a limit order (demo - will be cancelled immediately)
        print("\n6. Placing a demo limit order...")
        
        # Create a far out-of-the-money option for demo
        demo_strike = atm_strike + 50  # Far OTM
        demo_contract = OptionContract(
            symbol="SPY",
            expiry=nearest_expiry,
            strike=demo_strike,
            right=OptionRight.CALL
        )
        
        demo_order = Order(
            contract=demo_contract,
            action=OrderAction.BUY,
            quantity=1,
            order_type=OrderType.LIMIT,
            limit_price=0.01  # Very low price for demo
        )
        
        order_id = client.place_option_order(demo_order)
        
        if order_id:
            print(f"✅ Demo order placed successfully! Order ID: {order_id}")
            
            # 7. Check order status
            print("\n7. Checking order status...")
            time.sleep(1)  # Wait a moment for order to be processed
            
            status = client.get_order_status(order_id)
            if status:
                print(f"✅ Order status retrieved:")
                print(f"   Order ID: {status.order_id}")
                print(f"   Status: {status.status}")
                print(f"   Filled: {status.filled}")
                print(f"   Remaining: {status.remaining}")
            
            # 8. Cancel the demo order
            print("\n8. Cancelling demo order...")
            if client.cancel_order(order_id):
                print(f"✅ Demo order {order_id} cancelled successfully")
            else:
                print(f"❌ Failed to cancel demo order {order_id}")
        else:
            print("❌ Failed to place demo order")
        
        # 9. Get available symbols
        print("\n9. Getting available symbols...")
        symbols = client.get_available_symbols()
        print(f"✅ Available symbols ({len(symbols)}): {', '.join(symbols[:10])}...")
        
        # 10. Show cache statistics
        print("\n10. Cache statistics...")
        cache_stats = client.get_cache_stats()
        print(f"✅ Cache contains {cache_stats['cache_size']} items")
        
        print("\n" + "="*60)
        print("✅ All examples completed successfully!")
        print("✅ IB Options Trading POC is working correctly")
        print("="*60)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  Examples interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Error in examples: {e}", exc_info=True)
        print(f"\n❌ Error: {e}")
        return 1
    finally:
        # Always disconnect
        print("\nDisconnecting from Interactive Brokers...")
        client.disconnect()
        print("✅ Disconnected")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
