2025-06-10 22:21:23,130 - IBOptions - INFO - Attempting to connect to Interactive Brokers on 127.0.0.1:7497...
2025-06-10 22:21:23,550 - IBOptions - INFO - Successfully connected to Interactive Brokers
2025-06-10 23:16:31,928 - IBOptions - INFO - Retrieved market data for SPY C 580.0 ********
2025-06-10 23:16:31,928 - IBOptions - INFO - Retrieved market data for SPY C 580.0 ********
2025-06-10 23:27:14,836 - IBOptions - INFO - Attempting to connect to Interactive Brokers on 127.0.0.1:7497...
2025-06-10 23:27:15,175 - IBOptions - INFO - Successfully connected to Interactive Brokers
2025-06-10 23:27:17,931 - IBOptions - INFO - Testing real-time market data permissions...
2025-06-10 23:27:17,931 - IBOptions - INFO - Testing real-time market data permissions...
2025-06-10 23:27:19,939 - IBOptions - INFO - Real-time market data not available
2025-06-10 23:27:19,939 - IBOptions - INFO - Real-time market data not available
2025-06-10 23:27:20,437 - IBOptions - INFO - Testing delayed market data permissions...
2025-06-10 23:27:20,437 - IBOptions - INFO - Testing delayed market data permissions...
2025-06-10 23:27:22,443 - IBOptions - INFO - Delayed market data not available
2025-06-10 23:27:22,443 - IBOptions - INFO - Delayed market data not available
2025-06-10 23:27:25,695 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:25,695 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:25,695 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:30,714 - IBOptions - INFO - Qualified contract: Option(conId=787907567, symbol='SPY', lastTradeDateOrContractMonth='20250610', strike=557.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00557000', tradingClass='SPY')
2025-06-10 23:27:30,714 - IBOptions - INFO - Qualified contract: Option(conId=787907567, symbol='SPY', lastTradeDateOrContractMonth='20250610', strike=557.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00557000', tradingClass='SPY')
2025-06-10 23:27:30,714 - IBOptions - INFO - Qualified contract: Option(conId=787907567, symbol='SPY', lastTradeDateOrContractMonth='20250610', strike=557.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00557000', tradingClass='SPY')
2025-06-10 23:27:30,714 - IBOptions - INFO - Qualified contract: Option(conId=787907567, symbol='SPY', lastTradeDateOrContractMonth='20250610', strike=557.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00557000', tradingClass='SPY')
2025-06-10 23:27:30,820 - IBOptions - INFO - Requesting delayed market data (free)...
2025-06-10 23:27:30,820 - IBOptions - INFO - Requesting delayed market data (free)...
2025-06-10 23:27:30,820 - IBOptions - INFO - Requesting delayed market data (free)...
2025-06-10 23:27:30,820 - IBOptions - INFO - Requesting delayed market data (free)...
2025-06-10 23:27:35,918 - IBOptions - WARNING - No market data available for SPY C 557.0 20250610
2025-06-10 23:27:35,918 - IBOptions - WARNING - No market data available for SPY C 557.0 20250610
2025-06-10 23:27:35,918 - IBOptions - WARNING - No market data available for SPY C 557.0 20250610
2025-06-10 23:27:35,918 - IBOptions - WARNING - No market data available for SPY C 557.0 20250610
2025-06-10 23:27:35,919 - IBOptions - WARNING - This might be due to: 1) Contract not actively traded, 2) Market closed, 3) Invalid contract
2025-06-10 23:27:35,919 - IBOptions - WARNING - This might be due to: 1) Contract not actively traded, 2) Market closed, 3) Invalid contract
2025-06-10 23:27:35,919 - IBOptions - WARNING - This might be due to: 1) Contract not actively traded, 2) Market closed, 3) Invalid contract
2025-06-10 23:27:35,919 - IBOptions - WARNING - This might be due to: 1) Contract not actively traded, 2) Market closed, 3) Invalid contract
2025-06-10 23:27:36,979 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:36,979 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:36,979 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:36,979 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:36,979 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:36,979 - IBOptions - INFO - Retrieved option chain for SPY
2025-06-10 23:27:36,981 - IBOptions - INFO - Using nearest expiration: 20250610
2025-06-10 23:27:36,981 - IBOptions - INFO - Using nearest expiration: 20250610
2025-06-10 23:27:36,981 - IBOptions - INFO - Using nearest expiration: 20250610
2025-06-10 23:27:36,981 - IBOptions - INFO - Using nearest expiration: 20250610
2025-06-10 23:27:36,981 - IBOptions - INFO - Using nearest expiration: 20250610
2025-06-10 23:27:36,981 - IBOptions - INFO - Using nearest expiration: 20250610
2025-06-10 23:27:38,330 - IBOptions - INFO - Current stock price: nan
2025-06-10 23:27:38,330 - IBOptions - INFO - Current stock price: nan
2025-06-10 23:27:38,330 - IBOptions - INFO - Current stock price: nan
2025-06-10 23:27:38,330 - IBOptions - INFO - Current stock price: nan
2025-06-10 23:27:38,330 - IBOptions - INFO - Current stock price: nan
2025-06-10 23:27:38,330 - IBOptions - INFO - Current stock price: nan
2025-06-10 23:27:38,331 - IBOptions - INFO - Selected 5 strikes around ATM: [150.0, 155.0, 160.0, 165.0, 170.0]
2025-06-10 23:27:38,331 - IBOptions - INFO - Selected 5 strikes around ATM: [150.0, 155.0, 160.0, 165.0, 170.0]
2025-06-10 23:27:38,331 - IBOptions - INFO - Selected 5 strikes around ATM: [150.0, 155.0, 160.0, 165.0, 170.0]
2025-06-10 23:27:38,331 - IBOptions - INFO - Selected 5 strikes around ATM: [150.0, 155.0, 160.0, 165.0, 170.0]
2025-06-10 23:27:38,331 - IBOptions - INFO - Selected 5 strikes around ATM: [150.0, 155.0, 160.0, 165.0, 170.0]
2025-06-10 23:27:38,331 - IBOptions - INFO - Selected 5 strikes around ATM: [150.0, 155.0, 160.0, 165.0, 170.0]
2025-06-10 23:27:38,332 - IBOptions - INFO - Getting market data for strike 150.0...
2025-06-10 23:27:38,332 - IBOptions - INFO - Getting market data for strike 150.0...
2025-06-10 23:27:38,332 - IBOptions - INFO - Getting market data for strike 150.0...
2025-06-10 23:27:38,332 - IBOptions - INFO - Getting market data for strike 150.0...
2025-06-10 23:27:38,332 - IBOptions - INFO - Getting market data for strike 150.0...
2025-06-10 23:27:38,332 - IBOptions - INFO - Getting market data for strike 150.0...
2025-06-10 23:27:39,048 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 C
2025-06-10 23:27:39,048 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 C
2025-06-10 23:27:39,048 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 C
2025-06-10 23:27:39,048 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 C
2025-06-10 23:27:39,048 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 C
2025-06-10 23:27:39,048 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 C
2025-06-10 23:27:39,048 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 C
2025-06-10 23:27:39,676 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 P
2025-06-10 23:27:39,676 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 P
2025-06-10 23:27:39,676 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 P
2025-06-10 23:27:39,676 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 P
2025-06-10 23:27:39,676 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 P
2025-06-10 23:27:39,676 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 P
2025-06-10 23:27:39,676 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 P
2025-06-10 23:27:39,676 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 150.0 P
2025-06-10 23:27:39,889 - IBOptions - INFO - Getting market data for strike 155.0...
2025-06-10 23:27:39,889 - IBOptions - INFO - Getting market data for strike 155.0...
2025-06-10 23:27:39,889 - IBOptions - INFO - Getting market data for strike 155.0...
2025-06-10 23:27:39,889 - IBOptions - INFO - Getting market data for strike 155.0...
2025-06-10 23:27:39,889 - IBOptions - INFO - Getting market data for strike 155.0...
2025-06-10 23:27:39,889 - IBOptions - INFO - Getting market data for strike 155.0...
2025-06-10 23:27:39,889 - IBOptions - INFO - Getting market data for strike 155.0...
2025-06-10 23:27:39,889 - IBOptions - INFO - Getting market data for strike 155.0...
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:40,610 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 C
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,237 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 155.0 P
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:41,442 - IBOptions - INFO - Getting market data for strike 160.0...
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,095 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 C
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:42,895 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 160.0 P
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,109 - IBOptions - INFO - Getting market data for strike 165.0...
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:43,838 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 C
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,476 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 165.0 P
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:44,683 - IBOptions - INFO - Getting market data for strike 170.0...
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:45,459 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 C
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,089 - IBOptions - ERROR - Could not qualify option contract: SPY 20250610 170.0 P
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:27:46,293 - IBOptions - INFO - Retrieved option chain with prices for SPY expiry 20250610
2025-06-10 23:33:00,404 - IBOptions - INFO - Attempting to connect to Interactive Brokers on 127.0.0.1:7497...
2025-06-10 23:33:00,730 - IBOptions - INFO - Successfully connected to Interactive Brokers
2025-06-10 23:33:22,572 - IBOptions - INFO - Attempting to connect to Interactive Brokers on 127.0.0.1:7497...
2025-06-10 23:33:22,898 - IBOptions - INFO - Successfully connected to Interactive Brokers
2025-06-10 23:34:19,042 - IBOptions - INFO - Testing real-time market data permissions...
2025-06-10 23:34:19,042 - IBOptions - INFO - Testing real-time market data permissions...
2025-06-10 23:34:21,061 - IBOptions - INFO - Real-time market data not available
2025-06-10 23:34:21,061 - IBOptions - INFO - Real-time market data not available
2025-06-10 23:34:21,575 - IBOptions - INFO - Testing delayed market data permissions...
2025-06-10 23:34:21,575 - IBOptions - INFO - Testing delayed market data permissions...
2025-06-10 23:34:23,574 - IBOptions - INFO - Delayed market data not available
2025-06-10 23:34:23,574 - IBOptions - INFO - Delayed market data not available
2025-06-10 23:35:07,762 - IBOptions - INFO - Testing real-time market data permissions...
2025-06-10 23:35:07,762 - IBOptions - INFO - Testing real-time market data permissions...
2025-06-10 23:35:07,762 - IBOptions - INFO - Testing real-time market data permissions...
2025-06-10 23:35:09,765 - IBOptions - INFO - Real-time market data not available
2025-06-10 23:35:09,765 - IBOptions - INFO - Real-time market data not available
2025-06-10 23:35:09,765 - IBOptions - INFO - Real-time market data not available
2025-06-10 23:35:10,281 - IBOptions - INFO - Testing delayed market data permissions...
2025-06-10 23:35:10,281 - IBOptions - INFO - Testing delayed market data permissions...
2025-06-10 23:35:10,281 - IBOptions - INFO - Testing delayed market data permissions...
2025-06-10 23:35:12,275 - IBOptions - INFO - Delayed market data not available
2025-06-10 23:35:12,275 - IBOptions - INFO - Delayed market data not available
2025-06-10 23:35:12,275 - IBOptions - INFO - Delayed market data not available
2025-06-10 23:49:04,812 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-10 23:49:11,852 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-10 23:53:20,697 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-10 23:53:20,698 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-10 23:53:20,698 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-10 23:53:22,720 - IBOptions.ConnectionManager - ERROR - Connection attempt 1 failed: [WinError 1225] The remote computer refused the network connection
2025-06-10 23:53:22,721 - IBOptions.ConnectionManager - INFO - Retrying in 1.0 seconds...
2025-06-10 23:53:23,722 - IBOptions.ConnectionManager - INFO - Connection attempt 2/3
2025-06-10 23:53:23,722 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-10 23:53:25,749 - IBOptions.ConnectionManager - ERROR - Connection attempt 2 failed: [WinError 1225] The remote computer refused the network connection
2025-06-10 23:53:25,749 - IBOptions.ConnectionManager - INFO - Retrying in 1.0 seconds...
2025-06-10 23:53:26,749 - IBOptions.ConnectionManager - INFO - Connection attempt 3/3
2025-06-10 23:53:26,749 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-10 23:53:28,775 - IBOptions.ConnectionManager - ERROR - Connection attempt 3 failed: [WinError 1225] The remote computer refused the network connection
2025-06-10 23:53:28,775 - IBOptions.ConnectionManager - ERROR - All connection attempts failed
2025-06-10 23:53:28,776 - IBOptions.IBOptionsClient - ERROR - Failed to connect client
2025-06-10 23:53:28,777 - IBOptions.IBOptionsClient - ERROR - Not connected to IB
2025-06-10 23:53:28,777 - IBOptions.IBOptionsClient - ERROR - Not connected to IB
2025-06-10 23:54:53,929 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-10 23:54:53,930 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-10 23:54:53,931 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-10 23:54:53,999 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-10 23:54:53,999 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-10 23:54:54,000 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-10 23:54:54,000 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-10 23:54:54,000 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-10 23:54:54,000 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-10 23:54:54,001 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-10 23:54:54,001 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-10 23:54:54,333 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-10 23:54:54,333 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-10 23:54:54,333 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-10 23:54:54,334 - IBOptions.IBOptionsClient - INFO - Fetching account balances
2025-06-10 23:54:54,590 - IBOptions.IBOptionsClient - INFO - Account balances retrieved successfully
2025-06-10 23:54:54,592 - IBOptions.IBOptionsClient - INFO - Fetching option positions
2025-06-10 23:54:59,636 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='********', strike=600.0, right=<OptionRight.CALL: 'C'>, exchange='', currency='USD', multiplier='100', trading_class='SPY', contract_id=*********, local_symbol='SPY   250611C00600000')
2025-06-10 23:54:59,637 - IBOptions.IBOptionsClient - INFO - Retrieved 1 option positions
2025-06-10 23:54:59,637 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-10 23:55:01,561 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=32): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-10 23:55:01,561 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-10 23:55:02,529 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for SPY: 33 expirations, 373 strikes
2025-06-10 23:55:02,530 - IBOptions.IBOptionsClient - INFO - Returning 20 common option symbols
2025-06-10 23:55:02,531 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=32): Can't find EId with tickerId:32
2025-06-10 23:55:03,175 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=33): No security definition has been found for the request
2025-06-10 23:55:03,175 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20241220', strike=450.0, right='C', exchange='SMART')
2025-06-10 23:55:03,175 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='20241220', strike=450.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-10 23:55:03,177 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 SPY C 600.0 20241220
2025-06-10 23:55:03,667 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=34): No security definition has been found for the request
2025-06-10 23:55:03,668 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20241220', strike=600.0, right='C', exchange='SMART')
2025-06-10 23:55:03,668 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract for order: OptionContract(symbol='SPY', expiry='20241220', strike=600.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-10 23:55:03,669 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 SPY C 600.0 20241220
2025-06-10 23:55:04,121 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=35): No security definition has been found for the request
2025-06-10 23:55:04,122 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20241220', strike=600.0, right='C', exchange='SMART')
2025-06-10 23:55:04,122 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract for order: OptionContract(symbol='SPY', expiry='20241220', strike=600.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-10 23:55:04,124 - IBOptions.IBOptionsClient - INFO - Fetching option chain for INVALID_SYMBOL_12345
2025-06-10 23:55:04,350 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=36): No security definition has been found for the request
2025-06-10 23:55:04,350 - IBOptions.ConnectionManager - ERROR - Contract: Stock(symbol='INVALID_SYMBOL_12345', exchange='SMART', currency='USD')
2025-06-10 23:55:04,352 - IBOptions.ConnectionManager - ERROR - IB Error 321 (reqId=37): Error validating request.-'co' : cause - Invalid contract id
2025-06-10 23:55:04,353 - IBOptions.IBOptionsClient - WARNING - No option chains found for INVALID_SYMBOL_12345
2025-06-10 23:55:04,907 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=38): No security definition has been found for the request
2025-06-10 23:55:04,908 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20200101', strike=-100.0, right='C', exchange='SMART')
2025-06-10 23:55:04,908 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='20200101', strike=-100.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-10 23:55:04,912 - IBOptions.ConnectionManager - INFO - Disconnecting from Interactive Brokers
2025-06-10 23:55:04,912 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-10 23:55:04,912 - IBOptions.IBOptionsClient - INFO - Client disconnected
2025-06-10 23:56:22,991 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-10 23:56:22,993 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-10 23:56:22,993 - IBOptions.Main - INFO - ============================================================
2025-06-10 23:56:22,993 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-10 23:56:22,993 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-10 23:56:22,993 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-10 23:56:22,993 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-10 23:56:22,993 - IBOptions.Main - INFO - ============================================================
2025-06-10 23:56:22,994 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-10 23:56:22,994 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-10 23:56:22,994 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-10 23:56:22,995 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-10 23:56:23,011 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-10 23:56:23,012 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-10 23:56:23,013 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-10 23:56:23,013 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-10 23:56:23,013 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-10 23:56:23,014 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-10 23:56:23,014 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-10 23:56:23,014 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-10 23:56:23,427 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-10 23:56:23,427 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-10 23:56:23,427 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-10 23:56:23,428 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-10 23:56:34,661 - IBOptions.IBOptionsApp - INFO - Received signal 2, shutting down...
2025-06-10 23:56:34,661 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-10 23:56:34,661 - IBOptions.ConnectionManager - INFO - Disconnecting from Interactive Brokers
2025-06-10 23:56:34,662 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-10 23:56:34,662 - IBOptions.IBOptionsClient - INFO - Client disconnected
2025-06-10 23:56:34,662 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-10 23:56:34,662 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-10 23:56:34,662 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-10 23:56:34,662 - IBOptions.PerformanceMonitor - INFO - Performance monitoring stopped
2025-06-10 23:56:37,162 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-10 23:56:37,163 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-10 23:56:37,163 - IBOptions.Main - INFO - ============================================================
2025-06-10 23:56:37,164 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-10 23:56:37,164 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-10 23:56:37,164 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-10 23:56:37,164 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-10 23:56:37,164 - IBOptions.Main - INFO - ============================================================
2025-06-10 23:56:37,165 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-10 23:56:37,165 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-10 23:56:37,165 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-10 23:56:37,166 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-10 23:56:37,174 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-10 23:56:37,175 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-10 23:56:37,176 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-10 23:56:37,176 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-10 23:56:37,176 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-10 23:56:37,178 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-10 23:56:37,178 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-10 23:56:37,178 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-10 23:56:37,587 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-10 23:56:37,587 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-10 23:56:37,587 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-10 23:56:37,587 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-10 23:56:55,873 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=29): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-10 23:56:55,873 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=787911382, symbol='SPY', lastTradeDateOrContractMonth='********', strike=580.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00580000', tradingClass='SPY')
2025-06-10 23:56:55,982 - IBOptions.IBOptionsClient - ERROR - Error fetching market data for OptionContract(symbol='SPY', expiry='********', strike=580.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None): cannot convert float NaN to integer
