from ib_options.connect import connect_ib, setup_logger
from ib_options.account import get_account_balances, get_open_option_positions
from ib_options.market_data import get_option_chain, get_option_market_data, get_option_chain_with_prices, check_market_data_permissions
from ib_options.orders import create_option_contract, place_option_order, get_order_status, cancel_order
import os
import sys

def ensure_log_directory():
    """Ensure the logs directory exists"""
    if not os.path.exists('logs'):
        os.makedirs('logs')

def display_menu():
    """Display the main menu options"""
    print("\n" + "="*55)
    print("===== Interactive Brokers Options Trading Menu =====")
    print("="*55)
    print("1. Fetch Account Balances")
    print("2. Fetch Open Option Positions")
    print("3. Fetch Option Chain for Symbol")
    print("4. Get Option Market Data (Single Contract)")
    print("5. Get Option Chain with Prices (Enhanced)")
    print("6. Place Option Order")
    print("7. Check Order Status")
    print("8. Cancel Order")
    print("9. Check Market Data Permissions")
    print("10. Exit")
    print("="*55)
    return input("Enter your choice (1-10): ")

def handle_menu_choice(ib, choice):
    """Handle the user's menu selection"""
    if choice == '1':
        # Fetch account balances
        print("\n--- Account Balances ---")
        balances = get_account_balances(ib)
        if balances:
            for key, value in balances.items():
                print(f"{key}: ${value:,.2f}")
        else:
            print("Failed to retrieve account balances.")
        
    elif choice == '2':
        # Fetch open option positions
        print("\n--- Open Option Positions ---")
        positions = get_open_option_positions(ib)
        if positions:
            for i, pos in enumerate(positions, 1):
                print(f"{i}. {pos['symbol']} {pos['right']} {pos['strike']} {pos['expiry']} - Qty: {pos['position']}, Avg Cost: {pos['avgCost']}")
                if pos['marketPrice'] > 0:
                    print(f"   Market Price: ${pos['marketPrice']:.2f}, Market Value: ${pos['marketValue']:.2f}, P&L: ${pos['pnl']:.2f}")
        else:
            print("No open option positions found.")
            
    elif choice == '3':
        # Fetch option chain
        symbol = input("Enter symbol (e.g., SPY): ").upper()
        print(f"\n--- Fetching Option Chain for {symbol} ---")
        chains = get_option_chain(ib, symbol)
        
        if chains:
            chain = next(iter(chains))
            print(f"Exchange: {chain.exchange}")
            print(f"Available expirations: {', '.join(sorted(chain.expirations)[:5])}...")
            print(f"Available strikes: {', '.join([str(s) for s in sorted(chain.strikes)[:5]])}...")
            
            # Ask if user wants to see more details
            more_details = input("\nShow more details? (y/n): ").lower()
            if more_details == 'y':
                expiry = input("Enter expiration date (from list above): ")
                if expiry in chain.expirations:
                    print(f"\nStrikes for {expiry}:")
                    strikes = sorted(chain.strikes)
                    for i in range(0, len(strikes), 5):
                        print(', '.join([str(s) for s in strikes[i:i+5]]))
                else:
                    print("Invalid expiration date.")
        else:
            print(f"No option chain found for {symbol}")
            
    elif choice == '4':
        # Get option market data
        print("\n--- Get Option Market Data ---")
        symbol = input("Enter underlying symbol (e.g., SPY): ").upper()
        right = input("Call or Put (C/P): ").upper()
        strike = float(input("Strike price: "))
        expiry = input("Expiration date (YYYYMMDD): ")
        
        print("\nRequesting market data (using delayed data - free)...")
        market_data = get_option_market_data(ib, symbol, expiry, strike, right, use_delayed=True)
        if market_data:
            print(f"\nMarket Data for {symbol} {right} {strike} {expiry}:")
            print(f"Contract ID: {market_data['contract_id']}")
            print(f"Local Symbol: {market_data['local_symbol']}")
            print(f"Data Type: {market_data['data_type']}")
            print(f"Bid: ${market_data['bid']:.2f}")
            print(f"Ask: ${market_data['ask']:.2f}")
            print(f"Mid: ${market_data['mid']:.2f}")
            print(f"Last: ${market_data['last']:.2f}")
            print(f"Close: ${market_data['close']:.2f}")
            print(f"Volume: {market_data['volume']}")
            print(f"Open Interest: {market_data['open_interest']}")
            print(f"Implied Volatility: {market_data['implied_volatility']:.4f}")

            # Show Greeks if available
            if market_data['delta'] != 0:
                print(f"\nGreeks:")
                print(f"Delta: {market_data['delta']:.4f}")
                print(f"Gamma: {market_data['gamma']:.4f}")
                print(f"Theta: {market_data['theta']:.4f}")
                print(f"Vega: {market_data['vega']:.4f}")
        else:
            print("Failed to retrieve market data.")
            print("This could be due to:")
            print("1. Invalid contract (check symbol, expiry, strike)")
            print("2. Market is closed")
            print("3. Contract is not actively traded")
            print("4. Market data subscription issues")

    elif choice == '5':
        # Get option chain with prices (enhanced)
        print("\n--- Get Option Chain with Prices (Enhanced) ---")
        symbol = input("Enter underlying symbol (e.g., SPY): ").upper()

        # Ask for expiry or use nearest
        use_nearest = input("Use nearest expiration? (y/n): ").lower()
        expiry = None
        if use_nearest != 'y':
            expiry = input("Enter expiration date (YYYYMMDD): ")

        max_strikes = input("Number of strikes to show (default 10): ")
        try:
            max_strikes = int(max_strikes) if max_strikes else 10
        except ValueError:
            max_strikes = 10

        print(f"\nFetching option chain with prices for {symbol}...")
        print("This may take a moment as we retrieve market data for multiple contracts...")

        chain_data = get_option_chain_with_prices(ib, symbol, expiry, max_strikes)
        if chain_data:
            print(f"\nOption Chain for {symbol} - Expiry: {chain_data['expiry']}")
            print(f"Current Stock Price: ${chain_data['current_price']:.2f}")
            print("\n" + "="*80)
            print(f"{'Strike':<8} {'Call Bid':<10} {'Call Ask':<10} {'Call Last':<10} {'Put Bid':<10} {'Put Ask':<10} {'Put Last':<10}")
            print("="*80)

            for i, strike in enumerate(chain_data['strikes']):
                call_data = chain_data['calls'][i] if i < len(chain_data['calls']) else {}
                put_data = chain_data['puts'][i] if i < len(chain_data['puts']) else {}

                call_bid = f"${call_data.get('bid', 0):.2f}" if call_data.get('bid', 0) > 0 else "N/A"
                call_ask = f"${call_data.get('ask', 0):.2f}" if call_data.get('ask', 0) > 0 else "N/A"
                call_last = f"${call_data.get('last', 0):.2f}" if call_data.get('last', 0) > 0 else "N/A"
                put_bid = f"${put_data.get('bid', 0):.2f}" if put_data.get('bid', 0) > 0 else "N/A"
                put_ask = f"${put_data.get('ask', 0):.2f}" if put_data.get('ask', 0) > 0 else "N/A"
                put_last = f"${put_data.get('last', 0):.2f}" if put_data.get('last', 0) > 0 else "N/A"

                print(f"{strike:<8.1f} {call_bid:<10} {call_ask:<10} {call_last:<10} {put_bid:<10} {put_ask:<10} {put_last:<10}")
        else:
            print("Failed to retrieve option chain with prices.")

    elif choice == '6':
        # Place option order
        print("\n--- Place Option Order ---")
        symbol = input("Enter underlying symbol (e.g., SPY): ").upper()
        right = input("Call or Put (C/P): ").upper()
        strike = float(input("Strike price: "))
        expiry = input("Expiration date (YYYYMMDD): ")
        action = input("BUY or SELL: ").upper()
        quantity = int(input("Number of contracts: "))
        order_type = input("Order type (MKT/LMT): ").upper()
        
        limit_price = None
        if order_type == 'LMT':
            limit_price = float(input("Limit price: "))
        
        # Create contract and place order
        contract = create_option_contract(symbol, expiry, strike, right)
        trade = place_option_order(ib, contract, action, quantity, order_type, limit_price)
        
        if trade:
            print(f"Order placed successfully. Order ID: {trade.order.orderId}")
            
            # Wait for order status update
            ib.sleep(1)
            status = get_order_status(ib, trade)
            if status:
                print(f"Status: {status['status']}")
                print(f"Filled: {status['filled']}")
                print(f"Remaining: {status['remaining']}")
        else:
            print("Failed to place order.")
            
    elif choice == '7':
        # Check order status
        print("\n--- Order Status ---")
        check_specific = input("Check specific order? (y/n): ").lower()

        if check_specific == 'y':
            order_id = int(input("Enter order ID: "))
            # Find the trade by order ID
            trades = ib.trades()
            trade = next((t for t in trades if t.order.orderId == order_id), None)

            if trade:
                status = get_order_status(ib, trade)
                if status:
                    print(f"Order ID: {status['orderId']}")
                    print(f"Status: {status['status']}")
                    print(f"Filled: {status['filled']}")
                    print(f"Remaining: {status['remaining']}")
                    print(f"Avg Fill Price: {status['avgFillPrice']}")
                else:
                    print("Failed to get order status.")
            else:
                print(f"Order ID {order_id} not found.")
        else:
            # Show all open orders
            statuses = get_order_status(ib)
            if statuses:
                for status in statuses:
                    print(f"Order ID: {status['orderId']}, Status: {status['status']}, "
                          f"Filled: {status['filled']}, Remaining: {status['remaining']}")
            else:
                print("No open orders found.")

    elif choice == '8':
        # Cancel order
        print("\n--- Cancel Order ---")
        order_id = int(input("Enter order ID to cancel: "))

        # Find the trade by order ID
        trades = ib.trades()
        trade = next((t for t in trades if t.order.orderId == order_id), None)

        if trade:
            success = cancel_order(ib, trade)
            if success:
                print(f"Order {order_id} cancelled successfully.")
            else:
                print(f"Failed to cancel order {order_id}.")
        else:
            print(f"Order ID {order_id} not found.")

    elif choice == '9':
        # Check market data permissions
        print("\n--- Market Data Permissions ---")
        print("Checking market data permissions...")
        permissions = check_market_data_permissions(ib)

        print(f"\nMarket Data Permission Status:")
        print(f"Real-time data: {'✓ Available' if permissions.get('real_time') else '✗ Not available'}")
        print(f"Delayed data: {'✓ Available' if permissions.get('delayed') else '✗ Not available'}")

        if permissions.get('error'):
            print(f"Error: {permissions['error']}")

        if not permissions.get('real_time') and not permissions.get('delayed'):
            print("\nNo market data permissions detected.")
            print("Please check your IB account settings and market data subscriptions.")
        elif permissions.get('delayed') and not permissions.get('real_time'):
            print("\nDelayed market data is available (free).")
            print("For real-time data, you may need to subscribe to market data feeds.")

    else:
        print("Invalid choice. Please try again.")
