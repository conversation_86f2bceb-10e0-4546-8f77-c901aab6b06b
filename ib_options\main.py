from ib_options.connect import connect_ib, setup_logger
from ib_options.account import get_account_balances, get_open_option_positions
from ib_options.market_data import get_option_chain, get_option_market_data
from ib_options.orders import create_option_contract, place_option_order, get_order_status, cancel_order
import os
import sys

def ensure_log_directory():
    """Ensure the logs directory exists"""
    if not os.path.exists('logs'):
        os.makedirs('logs')

def display_menu():
    """Display the main menu options"""
    print("\n===== Interactive Brokers Options Trading Menu =====")
    print("1. Fetch Account Balances")
    print("2. Fetch Open Option Positions")
    print("3. Fetch Option Chain for Symbol")
    print("4. Get Option Market Data")
    print("5. Place Option Order")
    print("6. Check Order Status")
    print("7. Cancel Order")
    print("8. Exit")
    print("=================================================")
    return input("Enter your choice (1-8): ")

def handle_menu_choice(ib, choice):
    """Handle the user's menu selection"""
    if choice == '1':
        # Fetch account balances
        print("\n--- Account Balances ---")
        balances = get_account_balances(ib)
        if balances:
            for key, value in balances.items():
                print(f"{key}: ${value:,.2f}")
        else:
            print("Failed to retrieve account balances.")
        
    elif choice == '2':
        # Fetch open option positions
        print("\n--- Open Option Positions ---")
        positions = get_open_option_positions(ib)
        if positions:
            for i, pos in enumerate(positions, 1):
                print(f"{i}. {pos['symbol']} {pos['right']} {pos['strike']} {pos['expiry']} - Qty: {pos['position']}, Avg Cost: {pos['avgCost']}")
                if pos['marketPrice'] > 0:
                    print(f"   Market Price: ${pos['marketPrice']:.2f}, Market Value: ${pos['marketValue']:.2f}, P&L: ${pos['pnl']:.2f}")
        else:
            print("No open option positions found.")
            
    elif choice == '3':
        # Fetch option chain
        symbol = input("Enter symbol (e.g., SPY): ").upper()
        print(f"\n--- Fetching Option Chain for {symbol} ---")
        chains = get_option_chain(ib, symbol)
        
        if chains:
            chain = next(iter(chains))
            print(f"Exchange: {chain.exchange}")
            print(f"Available expirations: {', '.join(sorted(chain.expirations)[:5])}...")
            print(f"Available strikes: {', '.join([str(s) for s in sorted(chain.strikes)[:5]])}...")
            
            # Ask if user wants to see more details
            more_details = input("\nShow more details? (y/n): ").lower()
            if more_details == 'y':
                expiry = input("Enter expiration date (from list above): ")
                if expiry in chain.expirations:
                    print(f"\nStrikes for {expiry}:")
                    strikes = sorted(chain.strikes)
                    for i in range(0, len(strikes), 5):
                        print(', '.join([str(s) for s in strikes[i:i+5]]))
                else:
                    print("Invalid expiration date.")
        else:
            print(f"No option chain found for {symbol}")
            
    elif choice == '4':
        # Get option market data
        print("\n--- Get Option Market Data ---")
        symbol = input("Enter underlying symbol (e.g., SPY): ").upper()
        right = input("Call or Put (C/P): ").upper()
        strike = float(input("Strike price: "))
        expiry = input("Expiration date (YYYYMMDD): ")
        
        market_data = get_option_market_data(ib, symbol, expiry, strike, right)
        if market_data:
            print("\nMarket Data:")
            print(f"Bid: ${market_data['bid']:.2f}")
            print(f"Ask: ${market_data['ask']:.2f}")
            print(f"Last: ${market_data['last']:.2f}")
            print(f"Volume: {market_data['volume']}")
            print(f"Open Interest: {market_data['open_interest']}")
            print(f"Implied Volatility: {market_data['implied_volatility']:.4f}")
        else:
            print("Failed to retrieve market data.")
            
    elif choice == '5':
        # Place option order
        print("\n--- Place Option Order ---")
        symbol = input("Enter underlying symbol (e.g., SPY): ").upper()
        right = input("Call or Put (C/P): ").upper()
        strike = float(input("Strike price: "))
        expiry = input("Expiration date (YYYYMMDD): ")
        action = input("BUY or SELL: ").upper()
        quantity = int(input("Number of contracts: "))
        order_type = input("Order type (MKT/LMT): ").upper()
        
        limit_price = None
        if order_type == 'LMT':
            limit_price = float(input("Limit price: "))
        
        # Create contract and place order
        contract = create_option_contract(symbol, expiry, strike, right)
        trade = place_option_order(ib, contract, action, quantity, order_type, limit_price)
        
        if trade:
            print(f"Order placed successfully. Order ID: {trade.order.orderId}")
            
            # Wait for order status update
            ib.sleep(1)
            status = get_order_status(ib, trade)
            if status:
                print(f"Status: {status['status']}")
                print(f"Filled: {status['filled']}")
                print(f"Remaining: {status['remaining']}")
        else:
            print("Failed to place order.")
            
    elif choice == '6':
        # Check order status
        print("\n--- Order Status ---")
        check_specific = input("Check specific order? (y/n): ").lower()
