#!/usr/bin/env python3
"""
Test script to verify the market data subscription fixes.
This script tests the enhanced market data functionality.
"""

import logging
from ib_options.connect import connect_ib, setup_logger
from ib_options.market_data import (
    get_option_market_data, 
    get_option_chain, 
    get_option_chain_with_prices,
    check_market_data_permissions
)

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('MarketDataTest')

def test_market_data_permissions(ib):
    """Test market data permissions"""
    logger.info("Testing market data permissions...")
    permissions = check_market_data_permissions(ib)
    
    print("\n" + "="*50)
    print("MARKET DATA PERMISSIONS TEST")
    print("="*50)
    print(f"Real-time data: {'✓ Available' if permissions.get('real_time') else '✗ Not available'}")
    print(f"Delayed data: {'✓ Available' if permissions.get('delayed') else '✗ Not available'}")
    
    if permissions.get('error'):
        print(f"Error: {permissions['error']}")
    
    return permissions.get('delayed') or permissions.get('real_time')

def test_single_option_market_data(ib):
    """Test single option market data retrieval"""
    logger.info("Testing single option market data...")
    
    # Test with SPY (liquid ETF)
    symbol = 'SPY'
    
    # Get option chain first to find valid contracts
    chains = get_option_chain(ib, symbol)
    if not chains:
        logger.error(f"No option chains found for {symbol}")
        return False
    
    chain = next(iter(chains))
    expiry = sorted(chain.expirations)[0]  # Nearest expiry
    strikes = sorted(chain.strikes)
    strike = strikes[len(strikes)//2]  # Middle strike
    
    print(f"\n" + "="*50)
    print("SINGLE OPTION MARKET DATA TEST")
    print("="*50)
    print(f"Testing: {symbol} {expiry} {strike} Call")
    
    # Test with delayed data
    market_data = get_option_market_data(ib, symbol, expiry, strike, 'C', use_delayed=True)
    
    if market_data:
        print("✓ Market data retrieved successfully")
        print(f"Contract ID: {market_data['contract_id']}")
        print(f"Local Symbol: {market_data['local_symbol']}")
        print(f"Data Type: {market_data['data_type']}")
        print(f"Bid: ${market_data['bid']:.2f}")
        print(f"Ask: ${market_data['ask']:.2f}")
        print(f"Last: ${market_data['last']:.2f}")
        print(f"Volume: {market_data['volume']}")
        print(f"Open Interest: {market_data['open_interest']}")
        
        if market_data['bid'] > 0 or market_data['ask'] > 0 or market_data['last'] > 0:
            print("✓ Market data contains valid prices")
            return True
        else:
            print("⚠ Market data retrieved but no prices available")
            return False
    else:
        print("✗ Failed to retrieve market data")
        return False

def test_option_chain_with_prices(ib):
    """Test option chain with prices"""
    logger.info("Testing option chain with prices...")
    
    symbol = 'SPY'
    
    print(f"\n" + "="*50)
    print("OPTION CHAIN WITH PRICES TEST")
    print("="*50)
    print(f"Testing: {symbol} option chain with prices")
    
    chain_data = get_option_chain_with_prices(ib, symbol, expiry=None, max_strikes=5)
    
    if chain_data:
        print("✓ Option chain with prices retrieved successfully")
        print(f"Symbol: {chain_data['symbol']}")
        print(f"Expiry: {chain_data['expiry']}")
        print(f"Current Price: ${chain_data['current_price']:.2f}")
        print(f"Number of strikes: {len(chain_data['strikes'])}")
        print(f"Number of calls: {len(chain_data['calls'])}")
        print(f"Number of puts: {len(chain_data['puts'])}")
        
        # Check if we have any valid prices
        valid_prices = False
        for call_data in chain_data['calls']:
            if call_data.get('bid', 0) > 0 or call_data.get('ask', 0) > 0:
                valid_prices = True
                break
        
        if valid_prices:
            print("✓ Option chain contains valid market prices")
            return True
        else:
            print("⚠ Option chain retrieved but no valid prices")
            return False
    else:
        print("✗ Failed to retrieve option chain with prices")
        return False

def main():
    """Main test function"""
    logger.info("Starting market data fix tests...")
    
    # Connect to IB
    ib = connect_ib(port=7497, client_id=1)
    if not ib:
        logger.error("Failed to connect to Interactive Brokers")
        return 1
    
    try:
        # Test 1: Market data permissions
        permissions_ok = test_market_data_permissions(ib)
        
        if not permissions_ok:
            print("\n⚠ No market data permissions available. Some tests may fail.")
        
        # Test 2: Single option market data
        single_data_ok = test_single_option_market_data(ib)
        
        # Test 3: Option chain with prices
        chain_data_ok = test_option_chain_with_prices(ib)
        
        # Summary
        print(f"\n" + "="*50)
        print("TEST SUMMARY")
        print("="*50)
        print(f"Market data permissions: {'✓ PASS' if permissions_ok else '✗ FAIL'}")
        print(f"Single option data: {'✓ PASS' if single_data_ok else '✗ FAIL'}")
        print(f"Option chain with prices: {'✓ PASS' if chain_data_ok else '✗ FAIL'}")
        
        if single_data_ok and chain_data_ok:
            print("\n🎉 All tests passed! Market data fixes are working.")
            return 0
        else:
            print("\n⚠ Some tests failed. Check the logs for details.")
            return 1
            
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        return 1
    finally:
        ib.disconnect()
        logger.info("Disconnected from Interactive Brokers")

if __name__ == "__main__":
    exit(main())
