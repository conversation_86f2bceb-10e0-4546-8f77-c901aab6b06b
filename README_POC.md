# IB Options Trading POC - Professional Edition

## Project Overview

This is a comprehensive Python Proof of Concept (POC) for Options trading with Interactive Brokers, developed as **Phase 1** of the GoTrade integration project. The POC provides all required functionality with professional-grade architecture, error handling, and performance monitoring.

### Project Team
- **<PERSON><PERSON><PERSON><PERSON>** (j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com)
- **<PERSON><PERSON><PERSON>** (<EMAIL>)  
- **<PERSON><PERSON><PERSON>** (<EMAIL>)

## Phase 1 Requirements ✅

### Core Functionality (All Implemented)
- ✅ **API Connection & Authentication**: Connect to Interactive Brokers API using paper trading account
- ✅ **Account Balances**: Fetch current account balances relevant to Options trading
- ✅ **Option Positions**: Fetch current open Options positions (SPY Calls/Puts for specific strike and expiry)
- ✅ **Option Chains**: Fetch available symbols for the asset class (option chains for underlying symbols)
- ✅ **Order Placement**: Place new single-leg Options orders (market orders, limit orders for specific option contracts)
- ✅ **Order Management**: Fetch the status of existing Options orders
- ✅ **Error Handling & Logging**: Basic error handling and logging
- ✅ **Testing & Documentation**: Testing and documentation

### Enhanced Features (Professional Edition)
- ✅ **Professional Architecture**: Modular design with proper separation of concerns
- ✅ **Configuration Management**: Environment-based configuration system
- ✅ **Performance Monitoring**: Built-in metrics and performance tracking
- ✅ **Comprehensive Testing**: Unit tests and integration tests
- ✅ **Market Data Caching**: Intelligent caching for improved performance
- ✅ **Advanced Error Handling**: Robust error handling with retry logic
- ✅ **Logging System**: Professional logging with rotation and levels
- ✅ **Data Models**: Strongly typed data models using dataclasses
- ✅ **Connection Management**: Enhanced connection management with callbacks

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd ib-options-trading-poc

# Install dependencies
pip install -r ib_options/requirements.txt

# Create configuration file
python main.py --create-config
cp .env.sample .env
# Edit .env with your settings
```

### 2. Setup Interactive Brokers

1. Install and start **TWS (Trader Workstation)** or **IB Gateway**
2. Configure for **Paper Trading** (port 7497)
3. Enable **API connections** in TWS/Gateway settings
4. Set **Socket port** to 7497 and enable **API**

### 3. Run the Application

```bash
# Run interactive application
python main.py

# Run with specific options
python main.py --no-banner    # Skip banner
python main.py --test         # Run test suite
python main.py --benchmark    # Run benchmarks
```

## Architecture Overview

```
ib_options/
├── __init__.py           # Package initialization
├── config.py             # Configuration management
├── models.py             # Data models and types
├── connect.py            # Connection management
├── client.py             # Main API client
├── app.py                # Application interface
├── metrics.py            # Performance monitoring
├── account.py            # Account operations (legacy)
├── market_data.py        # Market data operations (legacy)
├── orders.py             # Order operations (legacy)
└── requirements.txt      # Dependencies

tests/
├── __init__.py
└── test_client.py        # Test suite

main.py                   # Main entry point
README_POC.md            # This documentation
```

## Core Components

### 1. IBOptionsClient (`client.py`)
Main client class providing all trading operations:
- Account balance retrieval
- Option position management
- Option chain data
- Market data with caching
- Order placement and management
- Symbol discovery

### 2. Data Models (`models.py`)
Strongly typed data models:
- `OptionContract`: Option contract specification
- `MarketData`: Market data with Greeks
- `Order`: Order specification
- `Position`: Position information
- `AccountBalance`: Account balance data

### 3. Configuration (`config.py`)
Environment-based configuration:
- IB connection settings
- Trading parameters
- Market data configuration
- Logging settings
- Performance monitoring

### 4. Performance Monitoring (`metrics.py`)
Built-in performance tracking:
- Latency measurements
- System resource monitoring
- Operation counters
- Metrics export

## Usage Examples

### Basic Usage

```python
from ib_options.client import IBOptionsClient
from ib_options.models import OptionContract, Order, OptionRight, OrderType, OrderAction

# Create client and connect
client = IBOptionsClient()
client.connect()

# Get account balances
balances = client.get_account_balances()
print(f"Available Funds: ${balances.available_funds:,.2f}")

# Get option chain
chain = client.get_option_chain("SPY")
print(f"SPY has {len(chain.expirations)} expirations")

# Get market data
contract = OptionContract(
    symbol="SPY",
    expiry="********",
    strike=450.0,
    right=OptionRight.CALL
)
market_data = client.get_option_market_data(contract)
print(f"Bid: ${market_data.bid:.2f}, Ask: ${market_data.ask:.2f}")

# Place order
order = Order(
    contract=contract,
    action=OrderAction.BUY,
    quantity=1,
    order_type=OrderType.LIMIT,
    limit_price=5.50
)
order_id = client.place_option_order(order)
print(f"Order placed: {order_id}")

# Disconnect
client.disconnect()
```

### Interactive Mode

```bash
python main.py
```

The interactive mode provides a menu-driven interface:
1. Get Account Balances
2. Get Open Option Positions  
3. Get Option Chain for Symbol
4. Get Option Market Data
5. Place Option Order (Market)
6. Place Option Order (Limit)
7. Get Order Status
8. Cancel Order
9. Get Available Symbols
10. System Status & Cache Info

## Testing

### Run Test Suite
```bash
python main.py --test
```

### Run Specific Tests
```bash
pytest tests/test_client.py -v
```

### Integration Tests
```bash
pytest tests/test_client.py::TestIntegration -v -m integration
```

## Performance Monitoring

### View Current Metrics
```bash
python main.py --export-metrics
```

### Run Benchmarks
```bash
python main.py --benchmark
```

### Metrics Include:
- API call latencies
- Memory usage
- CPU usage
- Network I/O
- Operation success rates
- Cache hit rates

## Configuration

### Environment Variables

Create `.env` file with:

```bash
# IB Connection
IB_HOST=127.0.0.1
IB_PORT=7497
IB_CLIENT_ID=1

# Trading
TRADING_EXCHANGE=SMART
TRADING_CURRENCY=USD
TRADING_PAPER_MODE=true

# Market Data
MARKET_DATA_DELAYED=true
MARKET_DATA_GREEKS=true

# Logging
LOG_LEVEL=INFO
LOG_DIR=logs

# Performance
PERF_METRICS=true
PERF_METRICS_INTERVAL=60
```

## Error Handling

The POC includes comprehensive error handling:
- Connection retry logic
- Market data subscription fallbacks
- Order validation
- Graceful degradation
- Detailed error logging

## Logging

Professional logging system:
- Configurable log levels
- File rotation
- Console and file output
- Structured logging format
- Performance metrics logging

## Ready for Phase 2: C++ Implementation

The POC architecture is designed for easy translation to C++:
- Clear separation of concerns
- Well-defined interfaces
- Minimal external dependencies
- Performance-optimized design
- Comprehensive error handling

## Ready for Phase 3: GoTrade Integration

The POC provides:
- Professional API design
- Comprehensive testing
- Performance metrics
- Error handling patterns
- Configuration management
- Documentation

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Ensure TWS/Gateway is running
   - Check port 7497 is configured
   - Verify API is enabled in TWS

2. **Market Data Errors**
   - Uses delayed data by default (free)
   - Check market hours
   - Verify symbol validity

3. **Order Placement Issues**
   - Ensure paper trading mode
   - Check account permissions
   - Verify contract details

### Support

For issues or questions, contact the development team:
- Jugraunaq Singh: <EMAIL>
- Aditya Pareek: <EMAIL>
- Ayush Jain: <EMAIL>

## License

This project is developed for GoTrade integration. All rights reserved.
