2025-06-11 00:00:47,679 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-11 00:00:47,680 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:00:47,680 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:00:47,700 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:00:47,701 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:00:47,701 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:00:47,701 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:00:47,701 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:00:47,701 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:00:47,702 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:00:47,702 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:00:48,014 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:00:48,014 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 00:00:48,015 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 00:00:48,015 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:00:49,082 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=30): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:00:49,082 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:00:51,069 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for SPY: 33 expirations, 373 strikes
2025-06-11 00:00:51,070 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:00:51,073 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=30): Can't find EId with tickerId:30
2025-06-11 00:00:51,913 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=33): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:00:51,913 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:00:53,925 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for SPY: 33 expirations, 373 strikes
2025-06-11 00:00:53,927 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=33): Can't find EId with tickerId:33
2025-06-11 00:00:54,482 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=34): No security definition has been found for the request
2025-06-11 00:00:54,482 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='********', strike=557.0, right='C', exchange='SMART')
2025-06-11 00:00:54,483 - IBOptions.IBOptionsClient - WARNING - Could not qualify contract: OptionContract(symbol='SPY', expiry='********', strike=557.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 00:00:54,483 - IBOptions.IBOptionsClient - INFO - Fetching option chain for QQQ
2025-06-11 00:00:57,231 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=37): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.QQQ NASDAQ.NMS/TOP/ALL
2025-06-11 00:00:57,231 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=320227571, symbol='QQQ', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='QQQ', tradingClass='NMS')
2025-06-11 00:00:59,018 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for QQQ: 33 expirations, 365 strikes
2025-06-11 00:00:59,019 - IBOptions.IBOptionsClient - INFO - Fetching option chain for QQQ
2025-06-11 00:00:59,020 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=37): Can't find EId with tickerId:37
2025-06-11 00:01:00,385 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=40): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.QQQ NASDAQ.NMS/TOP/ALL
2025-06-11 00:01:00,385 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=320227571, symbol='QQQ', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='QQQ', tradingClass='NMS')
2025-06-11 00:01:01,925 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for QQQ: 33 expirations, 365 strikes
2025-06-11 00:01:01,926 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=40): Can't find EId with tickerId:40
2025-06-11 00:01:02,477 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=41): No security definition has been found for the request
2025-06-11 00:01:02,477 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='QQQ', lastTradeDateOrContractMonth='********', strike=494.78, right='C', exchange='SMART')
2025-06-11 00:01:02,477 - IBOptions.IBOptionsClient - WARNING - Could not qualify contract: OptionContract(symbol='QQQ', expiry='********', strike=494.78, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='QQQ', contract_id=None, local_symbol=None)
2025-06-11 00:01:02,478 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:01:04,624 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=44): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:01:04,624 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:01:06,056 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for AAPL: 21 expirations, 103 strikes
2025-06-11 00:01:06,057 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:01:06,058 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=44): Can't find EId with tickerId:44
2025-06-11 00:01:06,848 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=47): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:01:06,849 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:01:08,388 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for AAPL: 21 expirations, 103 strikes
2025-06-11 00:01:08,392 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=47): Can't find EId with tickerId:47
2025-06-11 00:01:09,729 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 00:01:10,064 - IBOptions.IBOptionsClient - ERROR - Error fetching market data for OptionContract(symbol='AAPL', expiry='********', strike=215.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='AAPL', contract_id=None, local_symbol=None): cannot convert float NaN to integer
2025-06-11 00:01:10,065 - IBOptions.ConnectionManager - INFO - Disconnecting from Interactive Brokers
2025-06-11 00:01:10,066 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 00:01:10,066 - IBOptions.IBOptionsClient - INFO - Client disconnected
2025-06-11 00:03:14,667 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-11 00:03:14,668 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:03:14,668 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:03:14,687 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:03:14,687 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:03:14,688 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:03:14,688 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:03:14,688 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:03:14,689 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:03:14,689 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:03:14,689 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:03:14,825 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:03:14,825 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 00:03:14,825 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 00:03:14,827 - IBOptions.IBOptionsClient - INFO - Fetching account balances
2025-06-11 00:03:15,132 - IBOptions.IBOptionsClient - INFO - Account balances retrieved successfully
2025-06-11 00:03:15,133 - IBOptions.IBOptionsClient - INFO - Fetching option positions
2025-06-11 00:03:19,868 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='********', strike=600.0, right=<OptionRight.CALL: 'C'>, exchange='', currency='USD', multiplier='100', trading_class='SPY', contract_id=*********, local_symbol='SPY   250611C00600000')
2025-06-11 00:03:19,869 - IBOptions.IBOptionsClient - INFO - Retrieved 1 option positions
2025-06-11 00:03:19,870 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:03:20,722 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=32): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:03:20,722 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:03:22,714 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for SPY: 33 expirations, 373 strikes
2025-06-11 00:03:22,715 - IBOptions.IBOptionsClient - INFO - Returning 20 common option symbols
2025-06-11 00:03:22,716 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:03:22,717 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=32): Can't find EId with tickerId:32
2025-06-11 00:03:23,327 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=35): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:03:23,327 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:03:25,039 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for AAPL: 21 expirations, 103 strikes
2025-06-11 00:03:25,041 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=35): Can't find EId with tickerId:35
2025-06-11 00:03:25,370 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 00:03:25,707 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:03:26,734 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=41): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:03:26,735 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:03:28,129 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for AAPL: 21 expirations, 103 strikes
2025-06-11 00:03:28,132 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=41): Can't find EId with tickerId:41
2025-06-11 00:03:28,459 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 00:03:28,459 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 265.0 ********
2025-06-11 00:03:29,141 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 44
2025-06-11 00:03:30,142 - IBOptions.IBOptionsClient - INFO - Cancelling order 44
2025-06-11 00:03:30,142 - IBOptions.IBOptionsClient - INFO - Order 44 cancelled successfully
2025-06-11 00:03:30,143 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:03:30,412 - IBOptions.ConnectionManager - ERROR - IB Error 202 (reqId=44): Order Canceled - reason:
2025-06-11 00:03:30,685 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=47): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:03:30,686 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:03:32,697 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for AAPL: 21 expirations, 103 strikes
2025-06-11 00:03:32,698 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=47): Can't find EId with tickerId:47
2025-06-11 00:03:33,022 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 00:03:33,022 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 265.0 ********
2025-06-11 00:03:33,248 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 50
2025-06-11 00:03:34,249 - IBOptions.IBOptionsClient - INFO - Cancelling order 50
2025-06-11 00:03:34,249 - IBOptions.IBOptionsClient - INFO - Order 50 cancelled successfully
2025-06-11 00:03:34,250 - IBOptions.IBOptionsClient - ERROR - Error creating order status: OrderStatus.__init__() missing 1 required positional argument: 'status'
2025-06-11 00:03:34,250 - IBOptions.IBOptionsClient - ERROR - Error creating order status: OrderStatus.__init__() missing 1 required positional argument: 'status'
2025-06-11 00:03:34,251 - IBOptions.IBOptionsClient - INFO - Fetching option chain for INVALID_SYMBOL_12345
2025-06-11 00:03:34,481 - IBOptions.ConnectionManager - ERROR - IB Error 202 (reqId=50): Order Canceled - reason:
2025-06-11 00:03:34,700 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=51): No security definition has been found for the request
2025-06-11 00:03:34,701 - IBOptions.ConnectionManager - ERROR - Contract: Stock(symbol='INVALID_SYMBOL_12345', exchange='SMART', currency='USD')
2025-06-11 00:03:34,702 - IBOptions.ConnectionManager - ERROR - IB Error 321 (reqId=52): Error validating request.-'co' : cause - Invalid contract id
2025-06-11 00:03:34,703 - IBOptions.IBOptionsClient - WARNING - No option chains found for INVALID_SYMBOL_12345
2025-06-11 00:03:35,174 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=53): No security definition has been found for the request
2025-06-11 00:03:35,174 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20200101', strike=-100.0, right='C', exchange='SMART')
2025-06-11 00:03:35,175 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='20200101', strike=-100.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 00:03:35,178 - IBOptions.ConnectionManager - INFO - Disconnecting from Interactive Brokers
2025-06-11 00:03:35,178 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 00:03:35,178 - IBOptions.IBOptionsClient - INFO - Client disconnected
2025-06-11 00:04:31,254 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-11 00:04:31,255 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:04:31,255 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:04:31,276 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:04:31,277 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:04:31,277 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:04:31,278 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:04:31,278 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:04:31,278 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:04:31,278 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:04:31,279 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:04:31,478 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:04:31,479 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 00:04:31,479 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 00:04:31,480 - IBOptions.IBOptionsClient - INFO - Fetching account balances
2025-06-11 00:04:31,720 - IBOptions.IBOptionsClient - INFO - Account balances retrieved successfully
2025-06-11 00:04:31,721 - IBOptions.IBOptionsClient - INFO - Fetching option positions
2025-06-11 00:04:31,969 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='********', strike=600.0, right=<OptionRight.CALL: 'C'>, exchange='', currency='USD', multiplier='100', trading_class='SPY', contract_id=*********, local_symbol='SPY   250611C00600000')
2025-06-11 00:04:31,970 - IBOptions.IBOptionsClient - INFO - Retrieved 1 option positions
2025-06-11 00:04:31,970 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:04:32,725 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=57): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:04:32,725 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:04:34,719 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for SPY: 33 expirations, 373 strikes
2025-06-11 00:04:34,719 - IBOptions.IBOptionsClient - INFO - Returning 20 common option symbols
2025-06-11 00:04:34,720 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:04:34,722 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=57): Can't find EId with tickerId:57
2025-06-11 00:04:35,587 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=60): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:04:35,587 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:04:37,122 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for AAPL: 21 expirations, 103 strikes
2025-06-11 00:04:37,141 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=60): Can't find EId with tickerId:60
2025-06-11 00:04:37,470 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 00:04:37,699 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=63): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.AAPL JUL 11 '25 215 Call/TOP/ALL
2025-06-11 00:04:37,699 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 00:04:37,810 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:04:38,431 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=66): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:04:38,431 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:04:40,118 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for AAPL: 21 expirations, 103 strikes
2025-06-11 00:04:40,121 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=66): Can't find EId with tickerId:66
2025-06-11 00:04:40,444 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 00:04:40,444 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 265.0 ********
2025-06-11 00:04:40,671 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 69
2025-06-11 00:04:41,671 - IBOptions.IBOptionsClient - INFO - Cancelling order 69
2025-06-11 00:04:41,672 - IBOptions.IBOptionsClient - INFO - Order 69 cancelled successfully
2025-06-11 00:04:41,672 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:04:41,673 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=69): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 00:04:42,584 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=72): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:04:42,585 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:04:44,084 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for AAPL: 21 expirations, 103 strikes
2025-06-11 00:04:44,087 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=72): Can't find EId with tickerId:72
2025-06-11 00:04:44,411 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 00:04:44,412 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 265.0 ********
2025-06-11 00:04:44,640 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 75
2025-06-11 00:04:45,642 - IBOptions.IBOptionsClient - INFO - Cancelling order 75
2025-06-11 00:04:45,645 - IBOptions.IBOptionsClient - INFO - Order 75 cancelled successfully
2025-06-11 00:04:45,645 - IBOptions.IBOptionsClient - ERROR - Error creating order status: OrderStatus.__init__() missing 1 required positional argument: 'status'
2025-06-11 00:04:45,645 - IBOptions.IBOptionsClient - ERROR - Error creating order status: OrderStatus.__init__() missing 1 required positional argument: 'status'
2025-06-11 00:04:45,645 - IBOptions.IBOptionsClient - ERROR - Error creating order status: OrderStatus.__init__() missing 1 required positional argument: 'status'
2025-06-11 00:04:45,646 - IBOptions.IBOptionsClient - ERROR - Error creating order status: OrderStatus.__init__() missing 1 required positional argument: 'status'
2025-06-11 00:04:45,646 - IBOptions.IBOptionsClient - INFO - Fetching option chain for INVALID_SYMBOL_12345
2025-06-11 00:04:45,647 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=75): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 00:04:45,983 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=76): No security definition has been found for the request
2025-06-11 00:04:45,983 - IBOptions.ConnectionManager - ERROR - Contract: Stock(symbol='INVALID_SYMBOL_12345', exchange='SMART', currency='USD')
2025-06-11 00:04:45,985 - IBOptions.ConnectionManager - ERROR - IB Error 321 (reqId=77): Error validating request.-'co' : cause - Invalid contract id
2025-06-11 00:04:45,985 - IBOptions.IBOptionsClient - WARNING - No option chains found for INVALID_SYMBOL_12345
2025-06-11 00:04:46,436 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=78): No security definition has been found for the request
2025-06-11 00:04:46,436 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20200101', strike=-100.0, right='C', exchange='SMART')
2025-06-11 00:04:46,436 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='20200101', strike=-100.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 00:04:46,439 - IBOptions.ConnectionManager - INFO - Disconnecting from Interactive Brokers
2025-06-11 00:04:46,439 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 00:04:46,439 - IBOptions.IBOptionsClient - INFO - Client disconnected
2025-06-11 00:10:52,080 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-11 00:10:52,082 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 00:10:52,082 - IBOptions.Main - INFO - ============================================================
2025-06-11 00:10:52,082 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 00:10:52,082 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 00:10:52,082 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 00:10:52,082 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 00:10:52,082 - IBOptions.Main - INFO - ============================================================
2025-06-11 00:10:52,083 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 00:10:52,083 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 00:10:52,084 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:10:52,084 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:10:52,101 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:10:52,102 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:10:52,102 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:10:52,102 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:10:52,102 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:10:52,103 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:10:52,103 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:10:52,103 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:10:52,411 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:10:52,412 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 00:10:52,412 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 00:10:52,412 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-11 00:11:53,103 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14223.4 MB) exceeds limit (512 MB)
2025-06-11 00:11:53,333 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=79): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:11:53,334 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=580.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00580000', tradingClass='SPY')
2025-06-11 00:12:54,130 - IBOptions.PerformanceMonitor - WARNING - Memory usage (13211.1 MB) exceeds limit (512 MB)
2025-06-11 00:13:55,158 - IBOptions.PerformanceMonitor - WARNING - Memory usage (13857.6 MB) exceeds limit (512 MB)
2025-06-11 00:14:56,176 - IBOptions.PerformanceMonitor - WARNING - Memory usage (13845.3 MB) exceeds limit (512 MB)
2025-06-11 00:15:57,189 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14031.9 MB) exceeds limit (512 MB)
2025-06-11 00:16:58,205 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14129.7 MB) exceeds limit (512 MB)
2025-06-11 00:17:59,230 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14005.5 MB) exceeds limit (512 MB)
2025-06-11 00:19:00,253 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14181.5 MB) exceeds limit (512 MB)
2025-06-11 00:20:01,271 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14392.1 MB) exceeds limit (512 MB)
2025-06-11 00:20:11,786 - IBOptions.IBOptionsApp - INFO - Received signal 2, shutting down...
2025-06-11 00:20:11,786 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 00:20:11,787 - IBOptions.ConnectionManager - INFO - Disconnecting from Interactive Brokers
2025-06-11 00:20:11,787 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 00:20:11,787 - IBOptions.IBOptionsClient - INFO - Client disconnected
2025-06-11 00:20:11,787 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 00:20:11,787 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 00:20:11,787 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 00:20:11,787 - IBOptions.PerformanceMonitor - INFO - Performance monitoring stopped
2025-06-11 00:20:14,085 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-11 00:20:14,087 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 00:20:14,087 - IBOptions.Main - INFO - ============================================================
2025-06-11 00:20:14,087 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 00:20:14,088 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 00:20:14,088 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 00:20:14,088 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 00:20:14,088 - IBOptions.Main - INFO - ============================================================
2025-06-11 00:20:14,089 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 00:20:14,089 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 00:20:14,090 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:20:14,090 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:20:14,118 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:20:14,119 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:20:14,119 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:20:14,119 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:20:14,120 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:20:14,120 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:20:14,121 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:20:14,121 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:20:14,439 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:20:14,440 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 00:20:14,440 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 00:20:14,441 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-11 00:20:21,271 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:20:22,070 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=80): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:20:22,070 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:20:24,074 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for SPY: 33 expirations, 373 strikes
2025-06-11 00:21:11,987 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=80): Can't find EId with tickerId:80
2025-06-11 00:21:12,222 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=81): Invalid value in field # 541
2025-06-11 00:21:12,222 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='200250610', strike=160.0, right='C', exchange='SMART')
2025-06-11 00:21:12,222 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='200250610', strike=160.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 00:21:15,102 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14559.8 MB) exceeds limit (512 MB)
2025-06-11 00:22:16,120 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14487.4 MB) exceeds limit (512 MB)
2025-06-11 00:23:17,131 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14553.5 MB) exceeds limit (512 MB)
2025-06-11 00:24:18,143 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14646.8 MB) exceeds limit (512 MB)
2025-06-11 00:24:48,015 - IBOptions.IBOptionsApp - INFO - Received signal 2, shutting down...
2025-06-11 00:24:48,016 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 00:24:48,016 - IBOptions.ConnectionManager - INFO - Disconnecting from Interactive Brokers
2025-06-11 00:24:48,016 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 00:24:48,016 - IBOptions.IBOptionsClient - INFO - Client disconnected
2025-06-11 00:24:48,016 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 00:24:48,016 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 00:24:48,017 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 00:24:48,017 - IBOptions.PerformanceMonitor - INFO - Performance monitoring stopped
2025-06-11 00:24:51,921 - IBOptions.PerformanceMonitor - INFO - Performance monitoring started
2025-06-11 00:24:51,923 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 00:24:51,923 - IBOptions.Main - INFO - ============================================================
2025-06-11 00:24:51,923 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 00:24:51,923 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 00:24:51,923 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 00:24:51,924 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 00:24:51,924 - IBOptions.Main - INFO - ============================================================
2025-06-11 00:24:51,925 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 00:24:51,925 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 00:24:51,925 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:24:51,925 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:24:51,940 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:24:51,940 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:24:51,941 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:24:51,941 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:24:51,941 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:24:51,941 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:24:51,942 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:24:51,942 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:24:52,252 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:24:52,253 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 00:24:52,253 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 00:24:52,253 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-11 00:24:57,186 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:24:57,993 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=80): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:24:57,993 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:25:00,008 - IBOptions.IBOptionsClient - INFO - Option chain retrieved for SPY: 33 expirations, 373 strikes
2025-06-11 00:25:45,925 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=80): Can't find EId with tickerId:80
2025-06-11 00:25:46,384 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=81): No security definition has been found for the request
2025-06-11 00:25:46,384 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='********', strike=160.0, right='C', exchange='SMART')
2025-06-11 00:25:46,385 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='********', strike=160.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 00:25:52,940 - IBOptions.PerformanceMonitor - WARNING - Memory usage (14750.5 MB) exceeds limit (512 MB)
2025-06-11 00:26:22,460 - IBOptions.IBOptionsApp - INFO - Received signal 2, shutting down...
2025-06-11 00:26:22,460 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 00:26:22,460 - IBOptions.ConnectionManager - INFO - Disconnecting from Interactive Brokers
2025-06-11 00:26:22,460 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 00:26:22,460 - IBOptions.IBOptionsClient - INFO - Client disconnected
2025-06-11 00:26:22,461 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 00:26:22,461 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 00:26:22,461 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 00:26:22,461 - IBOptions.PerformanceMonitor - INFO - Performance monitoring stopped
2025-06-11 00:38:40,100 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error checking system resources: 'PerformanceConfig' object has no attribute 'memory_threshold'
2025-06-11 00:38:40,100 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error in monitoring loop: 'PerformanceConfig' object has no attribute 'monitoring_interval'
2025-06-11 00:38:40,104 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error checking system resources: 'PerformanceConfig' object has no attribute 'memory_threshold'
2025-06-11 00:38:40,104 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error in monitoring loop: 'PerformanceConfig' object has no attribute 'monitoring_interval'
2025-06-11 00:38:40,105 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error checking system resources: 'PerformanceConfig' object has no attribute 'memory_threshold'
2025-06-11 00:38:40,105 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error in monitoring loop: 'PerformanceConfig' object has no attribute 'monitoring_interval'
2025-06-11 00:38:40,105 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error checking system resources: 'PerformanceConfig' object has no attribute 'memory_threshold'
2025-06-11 00:38:40,106 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error in monitoring loop: 'PerformanceConfig' object has no attribute 'monitoring_interval'
2025-06-11 00:38:40,106 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error checking system resources: 'PerformanceConfig' object has no attribute 'memory_threshold'
2025-06-11 00:38:40,106 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error in monitoring loop: 'PerformanceConfig' object has no attribute 'monitoring_interval'
2025-06-11 00:38:40,107 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error checking system resources: 'PerformanceConfig' object has no attribute 'memory_threshold'
2025-06-11 00:38:40,108 - IBOptions.IBOptions.PerformanceMonitor - ERROR - Error in monitoring loop: 'PerformanceConfig' object has no attribute 'monitoring_interval'
2025-06-11 00:48:42,617 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:48:42,618 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:48:42,642 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:48:42,642 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:48:42,643 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:48:42,643 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:48:42,643 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:48:42,643 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:48:42,644 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:48:42,644 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:48:42,806 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:54:52,215 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:54:52,215 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:54:52,239 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:54:52,239 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:54:52,239 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:54:52,239 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:54:52,239 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:54:52,240 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:54:52,240 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:54:52,240 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:54:52,377 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:54:52,378 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 00:54:52,378 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 00:54:52,379 - IBOptions.IBOptionsClient - ERROR - Error getting account balances: 'Client' object has no attribute 'account'
2025-06-11 00:54:52,380 - IBOptions.IBOptionsClient - ERROR - Error getting option positions: Position.__init__() got an unexpected keyword argument 'symbol'
2025-06-11 00:54:52,380 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:54:53,397 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=119): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:54:53,397 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:54:54,398 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:54:54,399 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:54:54,400 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=119): Can't find EId with tickerId:119
2025-06-11 00:54:55,559 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=123): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:54:55,559 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:54:55,952 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:54:55,952 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:54:55,954 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=123): Can't find EId with tickerId:123
2025-06-11 00:54:57,003 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=127): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:54:57,003 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:54:58,010 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:54:58,012 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=127): Can't find EId with tickerId:127
2025-06-11 00:54:58,565 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=129): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.AAPL JUL 11 '25 215 Call/TOP/ALL
2025-06-11 00:54:58,565 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 00:54:58,565 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=129): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 00:54:58,566 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 00:54:59,914 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=131): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:54:59,914 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:55:00,564 - IBOptions.IBOptionsClient - ERROR - Error fetching market data: MarketData.__init__() got an unexpected keyword argument 'symbol'
2025-06-11 00:55:00,565 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:55:00,565 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=131): Can't find EId with tickerId:131
2025-06-11 00:55:01,658 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=135): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:55:01,658 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:55:02,107 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:55:02,107 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:55:02,109 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=135): Can't find EId with tickerId:135
2025-06-11 00:55:03,192 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=139): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:55:03,192 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:55:04,193 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:55:04,193 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 300.0 ********
2025-06-11 00:55:04,195 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=139): Can't find EId with tickerId:139
2025-06-11 00:55:04,516 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 141
2025-06-11 00:55:05,517 - IBOptions.IBOptionsClient - INFO - Order 141 canceled
2025-06-11 00:55:05,518 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:55:05,519 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=141): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 00:55:06,778 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=145): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:55:06,779 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:55:07,138 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:55:07,138 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:55:07,139 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=145): Can't find EId with tickerId:145
2025-06-11 00:55:08,211 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=149): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:55:08,211 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:55:09,225 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:55:09,225 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 300.0 ********
2025-06-11 00:55:09,226 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=149): Can't find EId with tickerId:149
2025-06-11 00:55:09,544 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 151
2025-06-11 00:55:10,546 - IBOptions.IBOptionsClient - INFO - Order 151 canceled
2025-06-11 00:55:10,547 - IBOptions.IBOptionsClient - ERROR - Error getting order status: OrderStatus.__init__() got an unexpected keyword argument 'why_held'
2025-06-11 00:55:10,548 - IBOptions.IBOptionsClient - INFO - Fetching option chain for INVALID_SYMBOL_12345
2025-06-11 00:55:10,549 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=151): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 00:55:10,866 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=152): No security definition has been found for the request
2025-06-11 00:55:10,866 - IBOptions.ConnectionManager - ERROR - Contract: Stock(symbol='INVALID_SYMBOL_12345', exchange='SMART', currency='USD')
2025-06-11 00:55:10,866 - IBOptions.IBOptionsClient - ERROR - Could not qualify stock contract: INVALID_SYMBOL_12345
2025-06-11 00:55:11,307 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=153): No security definition has been found for the request
2025-06-11 00:55:11,307 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20200101', strike=-100.0, right='C', exchange='SMART')
2025-06-11 00:55:11,308 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='20200101', strike=-100.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 00:55:11,313 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 00:55:11,313 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 00:59:15,292 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 00:59:15,293 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 00:59:15,317 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 00:59:15,318 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 00:59:15,318 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 00:59:15,318 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 00:59:15,318 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 00:59:15,319 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 00:59:15,319 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 00:59:15,319 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 00:59:15,519 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 00:59:15,520 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 00:59:15,520 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 00:59:15,521 - IBOptions.IBOptionsClient - ERROR - Error getting account balances: 'Client' object has no attribute 'account'
2025-06-11 00:59:15,522 - IBOptions.IBOptionsClient - ERROR - Error getting option positions: Position.__init__() got an unexpected keyword argument 'symbol'
2025-06-11 00:59:15,522 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:59:16,480 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=157): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:59:16,480 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:59:17,489 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:59:17,490 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:59:17,491 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=157): Can't find EId with tickerId:157
2025-06-11 00:59:18,452 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=161): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:59:18,452 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:59:19,007 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:59:19,007 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:59:19,009 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=161): Can't find EId with tickerId:161
2025-06-11 00:59:20,635 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=165): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:59:20,635 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:59:21,646 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:59:21,648 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=165): Can't find EId with tickerId:165
2025-06-11 00:59:22,213 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=167): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.AAPL JUL 11 '25 215 Call/TOP/ALL
2025-06-11 00:59:22,213 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 00:59:22,214 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=167): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 00:59:22,214 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 00:59:23,620 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=169): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:59:23,621 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:59:24,201 - IBOptions.IBOptionsClient - ERROR - Error fetching market data: MarketData.__init__() got an unexpected keyword argument 'symbol'
2025-06-11 00:59:24,202 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:59:24,203 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=169): Can't find EId with tickerId:169
2025-06-11 00:59:25,173 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=173): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:59:25,173 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:59:25,854 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:59:25,854 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:59:25,855 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=173): Can't find EId with tickerId:173
2025-06-11 00:59:26,911 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=177): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:59:26,911 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:59:27,902 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:59:27,902 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 300.0 ********
2025-06-11 00:59:27,903 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=177): Can't find EId with tickerId:177
2025-06-11 00:59:28,122 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 179
2025-06-11 00:59:29,123 - IBOptions.IBOptionsClient - INFO - Order 179 canceled
2025-06-11 00:59:29,124 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 00:59:29,124 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=179): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 00:59:30,234 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=183): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 00:59:30,235 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 00:59:30,760 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:59:30,760 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 00:59:30,761 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=183): Can't find EId with tickerId:183
2025-06-11 00:59:31,974 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=187): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 00:59:31,974 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 00:59:32,988 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 00:59:32,988 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 300.0 ********
2025-06-11 00:59:32,992 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=187): Can't find EId with tickerId:187
2025-06-11 00:59:33,310 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 189
2025-06-11 00:59:34,310 - IBOptions.IBOptionsClient - INFO - Order 189 canceled
2025-06-11 00:59:34,310 - IBOptions.IBOptionsClient - ERROR - Error getting order status: OrderStatus.__init__() got an unexpected keyword argument 'why_held'
2025-06-11 00:59:34,311 - IBOptions.IBOptionsClient - INFO - Fetching option chain for INVALID_SYMBOL_12345
2025-06-11 00:59:34,312 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=189): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 00:59:34,629 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=190): No security definition has been found for the request
2025-06-11 00:59:34,629 - IBOptions.ConnectionManager - ERROR - Contract: Stock(symbol='INVALID_SYMBOL_12345', exchange='SMART', currency='USD')
2025-06-11 00:59:34,629 - IBOptions.IBOptionsClient - ERROR - Could not qualify stock contract: INVALID_SYMBOL_12345
2025-06-11 00:59:35,065 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=191): No security definition has been found for the request
2025-06-11 00:59:35,065 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20200101', strike=-100.0, right='C', exchange='SMART')
2025-06-11 00:59:35,066 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='20200101', strike=-100.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 00:59:35,069 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 00:59:35,069 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 01:02:12,189 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:02:12,190 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:02:12,212 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 01:02:12,212 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 01:02:12,213 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 01:02:12,213 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 01:02:12,213 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 01:02:12,213 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 01:02:12,214 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 01:02:12,214 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 01:02:12,336 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 01:02:12,336 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 01:02:12,336 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 01:02:12,337 - IBOptions.IBOptionsClient - INFO - Fetching account balances
2025-06-11 01:02:12,643 - IBOptions.IBOptionsClient - INFO - Account balances retrieved successfully
2025-06-11 01:02:12,645 - IBOptions.IBOptionsClient - INFO - Fetching option positions
2025-06-11 01:02:13,109 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='********', strike=600.0, right=<OptionRight.CALL: 'C'>, exchange='', currency='USD', multiplier='100', trading_class='SPY', contract_id=*********, local_symbol='SPY   250611C00600000')
2025-06-11 01:02:13,109 - IBOptions.IBOptionsClient - INFO - Retrieved 1 option positions
2025-06-11 01:02:13,110 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:02:14,300 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=197): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:02:14,300 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:02:15,291 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 01:02:15,292 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 01:02:15,293 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=197): Can't find EId with tickerId:197
2025-06-11 01:02:16,170 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=201): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 01:02:16,170 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 01:02:16,872 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 01:02:16,872 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:02:16,874 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=201): Can't find EId with tickerId:201
2025-06-11 01:02:17,993 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=205): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:02:17,994 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:02:18,996 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 01:02:18,998 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=205): Can't find EId with tickerId:205
2025-06-11 01:02:19,568 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=207): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.AAPL JUL 11 '25 215 Call/TOP/ALL
2025-06-11 01:02:19,568 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 01:02:19,568 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=207): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 01:02:19,569 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 01:02:21,086 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=209): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 01:02:21,086 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 01:02:21,541 - IBOptions.IBOptionsClient - ERROR - Error fetching market data: MarketData.__init__() got an unexpected keyword argument 'symbol'
2025-06-11 01:02:21,542 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 01:02:21,543 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=209): Can't find EId with tickerId:209
2025-06-11 01:02:22,634 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=213): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 01:02:22,634 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 01:02:23,118 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 01:02:23,118 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:02:23,119 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=213): Can't find EId with tickerId:213
2025-06-11 01:02:24,295 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=217): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:02:24,295 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:02:25,297 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 01:02:25,297 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 300.0 ********
2025-06-11 01:02:25,298 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=217): Can't find EId with tickerId:217
2025-06-11 01:02:25,619 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 219
2025-06-11 01:02:26,621 - IBOptions.IBOptionsClient - INFO - Order 219 canceled
2025-06-11 01:02:26,622 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 01:02:26,623 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=219): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 01:02:27,767 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=223): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 01:02:27,767 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 01:02:28,293 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 01:02:28,294 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:02:28,295 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=223): Can't find EId with tickerId:223
2025-06-11 01:02:29,598 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=227): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:02:29,598 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:02:30,588 - IBOptions.IBOptionsClient - ERROR - Error getting option chain: OptionChain.__init__() got an unexpected keyword argument 'multiplier'
2025-06-11 01:02:30,588 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 300.0 ********
2025-06-11 01:02:30,590 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=227): Can't find EId with tickerId:227
2025-06-11 01:02:30,908 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 229
2025-06-11 01:02:31,909 - IBOptions.IBOptionsClient - INFO - Order 229 canceled
2025-06-11 01:02:31,910 - IBOptions.IBOptionsClient - INFO - Fetching option chain for INVALID_SYMBOL_12345
2025-06-11 01:02:32,138 - IBOptions.ConnectionManager - ERROR - IB Error 202 (reqId=229): Order Canceled - reason:
2025-06-11 01:02:32,345 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=230): No security definition has been found for the request
2025-06-11 01:02:32,345 - IBOptions.ConnectionManager - ERROR - Contract: Stock(symbol='INVALID_SYMBOL_12345', exchange='SMART', currency='USD')
2025-06-11 01:02:32,346 - IBOptions.IBOptionsClient - ERROR - Could not qualify stock contract: INVALID_SYMBOL_12345
2025-06-11 01:02:32,787 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=231): No security definition has been found for the request
2025-06-11 01:02:32,787 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20200101', strike=-100.0, right='C', exchange='SMART')
2025-06-11 01:02:32,787 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='20200101', strike=-100.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 01:02:32,790 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 01:02:32,790 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 01:19:24,582 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:19:53,830 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:19:53,832 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:19:53,832 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:19:53,855 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 01:19:53,856 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 01:19:53,856 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 01:19:53,857 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 01:19:53,857 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 01:19:53,857 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 01:19:53,857 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 01:19:53,858 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 01:19:53,978 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 01:19:53,978 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 01:19:53,978 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 01:19:53,979 - IBOptions.IBOptionsClient - INFO - Fetching account balances
2025-06-11 01:19:54,278 - IBOptions.IBOptionsClient - INFO - Account balances retrieved successfully
2025-06-11 01:19:54,280 - IBOptions.IBOptionsClient - INFO - Fetching option positions
2025-06-11 01:19:54,519 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='********', strike=600.0, right=<OptionRight.CALL: 'C'>, exchange='', currency='USD', multiplier='100', trading_class='SPY', contract_id=*********, local_symbol='SPY   250611C00600000')
2025-06-11 01:19:54,519 - IBOptions.IBOptionsClient - INFO - Retrieved 1 option positions
2025-06-11 01:19:54,520 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:19:55,611 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=237): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:19:55,611 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:19:56,613 - IBOptions.IBOptionsClient - INFO - Returning 20 common option symbols
2025-06-11 01:19:56,615 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 01:19:56,615 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=237): Can't find EId with tickerId:237
2025-06-11 01:19:57,560 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=241): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 01:19:57,560 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 01:19:58,144 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=241): Can't find EId with tickerId:241
2025-06-11 01:19:58,465 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 01:19:58,981 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=244): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.AAPL JUL 11 '25 215 Call/TOP/ALL
2025-06-11 01:19:58,981 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 01:19:58,982 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=244): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 01:19:58,982 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='AAPL', lastTradeDateOrContractMonth='********', strike=215.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='AAPL  250711C00215000', tradingClass='AAPL')
2025-06-11 01:20:00,283 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=246): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 01:20:00,283 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 01:20:00,937 - IBOptions.IBOptionsClient - ERROR - Error fetching market data: MarketData.__init__() got an unexpected keyword argument 'underlying_price'
2025-06-11 01:20:00,938 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 01:20:00,939 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=246): Can't find EId with tickerId:246
2025-06-11 01:20:02,039 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=250): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 01:20:02,039 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 01:20:02,513 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=250): Can't find EId with tickerId:250
2025-06-11 01:20:02,830 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 01:20:02,831 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 265.0 ********
2025-06-11 01:20:03,054 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 253
2025-06-11 01:20:04,054 - IBOptions.IBOptionsClient - INFO - Order 253 canceled
2025-06-11 01:20:04,055 - IBOptions.IBOptionsClient - INFO - Fetching option chain for AAPL
2025-06-11 01:20:04,056 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=253): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 01:20:05,101 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=257): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.Delayed market data is available.AAPL NASDAQ.NMS/TOP/ALL
2025-06-11 01:20:05,101 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS')
2025-06-11 01:20:05,668 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=257): Can't find EId with tickerId:257
2025-06-11 01:20:05,987 - IBOptions.IBOptionsClient - INFO - Found valid contract: AAPL C 215.0 ********
2025-06-11 01:20:05,987 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 AAPL C 265.0 ********
2025-06-11 01:20:06,209 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 260
2025-06-11 01:20:07,210 - IBOptions.IBOptionsClient - INFO - Order 260 canceled
2025-06-11 01:20:07,212 - IBOptions.IBOptionsClient - INFO - Fetching option chain for INVALID_SYMBOL_12345
2025-06-11 01:20:07,213 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=260): You are trying to submit an order without having market data for this instrument. IBKR strongly recommends against this kind of blind trading which may result in erroneous or unexpected trades. Restriction is specified in Precautionary Settings of Global Configuration/Presets.
2025-06-11 01:20:07,531 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=261): No security definition has been found for the request
2025-06-11 01:20:07,532 - IBOptions.ConnectionManager - ERROR - Contract: Stock(symbol='INVALID_SYMBOL_12345', exchange='SMART', currency='USD')
2025-06-11 01:20:07,532 - IBOptions.IBOptionsClient - ERROR - Could not qualify stock contract: INVALID_SYMBOL_12345
2025-06-11 01:20:07,976 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=262): No security definition has been found for the request
2025-06-11 01:20:07,977 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='20200101', strike=-100.0, right='C', exchange='SMART')
2025-06-11 01:20:07,977 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='20200101', strike=-100.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 01:20:07,981 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 01:20:07,981 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 01:25:39,113 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:25:39,115 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 01:25:39,115 - IBOptions.Main - INFO - ============================================================
2025-06-11 01:25:39,115 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 01:25:39,115 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 01:25:39,115 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 01:25:39,115 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 01:25:39,115 - IBOptions.Main - INFO - ============================================================
2025-06-11 01:25:39,116 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 01:25:39,116 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 01:25:39,116 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:25:39,117 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:25:39,141 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 01:25:39,141 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 01:25:39,141 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 01:25:39,141 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 01:25:39,142 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 01:25:39,142 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 01:25:39,142 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 01:25:39,142 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 01:25:39,267 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 01:25:39,267 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 01:25:39,267 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 01:25:39,268 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-11 01:34:24,056 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:34:25,035 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=266): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:34:25,036 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:35:21,767 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=266): Can't find EId with tickerId:266
2025-06-11 01:35:22,212 - IBOptions.ConnectionManager - ERROR - IB Error 200 (reqId=267): No security definition has been found for the request
2025-06-11 01:35:22,213 - IBOptions.ConnectionManager - ERROR - Contract: Option(symbol='SPY', lastTradeDateOrContractMonth='********', strike=150.0, right='C', exchange='SMART')
2025-06-11 01:35:22,213 - IBOptions.IBOptionsClient - ERROR - Could not qualify contract: OptionContract(symbol='SPY', expiry='********', strike=150.0, right=<OptionRight.CALL: 'C'>, exchange='SMART', currency='USD', multiplier=100, trading_class='SPY', contract_id=None, local_symbol=None)
2025-06-11 01:37:36,014 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:37:36,014 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:37:36,015 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:37:36,021 - IBOptions.ConnectionManager - ERROR - IB Error 326 (reqId=-1): Unable to connect as the client id is already in use. Retry with a unique client id.
2025-06-11 01:38:06,020 - IBOptions.ConnectionManager - ERROR - Connection attempt 1 failed: 
2025-06-11 01:38:06,020 - IBOptions.ConnectionManager - INFO - Retrying in 1.0 seconds...
2025-06-11 01:38:07,021 - IBOptions.ConnectionManager - INFO - Connection attempt 2/3
2025-06-11 01:38:07,021 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:38:07,026 - IBOptions.ConnectionManager - ERROR - IB Error 326 (reqId=-1): Unable to connect as the client id is already in use. Retry with a unique client id.
2025-06-11 01:40:01,319 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:40:01,326 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 01:40:01,326 - IBOptions.Main - INFO - ============================================================
2025-06-11 01:40:01,326 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 01:40:01,326 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 01:40:01,326 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 01:40:01,326 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 01:40:01,326 - IBOptions.Main - INFO - ============================================================
2025-06-11 01:40:01,327 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 01:40:01,327 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 01:40:01,328 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:40:01,328 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:40:01,335 - IBOptions.ConnectionManager - ERROR - IB Error 326 (reqId=-1): Unable to connect as the client id is already in use. Retry with a unique client id.
2025-06-11 01:40:37,894 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:41:48,572 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:41:48,574 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 01:41:48,574 - IBOptions.Main - INFO - ============================================================
2025-06-11 01:41:48,575 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 01:41:48,575 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 01:41:48,575 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 01:41:48,575 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 01:41:48,575 - IBOptions.Main - INFO - ============================================================
2025-06-11 01:41:48,576 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 01:41:48,576 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 01:41:48,576 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:41:48,577 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:41:48,600 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 01:41:48,601 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 01:41:48,601 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 01:41:48,602 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 01:41:48,602 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 01:41:48,602 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 01:41:48,603 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 01:41:48,603 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 01:41:49,003 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 01:41:49,003 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 01:41:49,003 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 01:41:49,003 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-11 01:42:00,133 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:42:01,109 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=266): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:42:01,109 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:42:08,444 - IBOptions.IBOptionsApp - INFO - Received signal 2, shutting down...
2025-06-11 01:42:08,444 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 01:42:08,444 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 01:42:08,444 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 01:42:08,445 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 01:42:08,445 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 01:42:08,445 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 01:42:11,926 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:42:11,928 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 01:42:11,928 - IBOptions.Main - INFO - ============================================================
2025-06-11 01:42:11,928 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 01:42:11,929 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 01:42:11,929 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 01:42:11,929 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 01:42:11,929 - IBOptions.Main - INFO - ============================================================
2025-06-11 01:42:11,930 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 01:42:11,930 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 01:42:11,931 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:42:11,931 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:42:11,947 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 01:42:11,947 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 01:42:11,947 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 01:42:11,947 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 01:42:11,948 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 01:42:11,948 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 01:42:11,948 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 01:42:11,948 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 01:42:12,072 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 01:42:12,072 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 01:42:12,072 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 01:42:12,073 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-11 01:42:16,541 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:42:17,511 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=266): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:42:17,512 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:42:36,163 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=266): Can't find EId with tickerId:266
2025-06-11 01:42:36,825 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=268): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:42:36,826 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=555.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00555000', tradingClass='SPY')
2025-06-11 01:42:37,125 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=268): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 555 Call/TOP/ALL
2025-06-11 01:42:37,126 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=555.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00555000', tradingClass='SPY')
2025-06-11 01:42:37,126 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=268): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 01:42:37,127 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=555.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00555000', tradingClass='SPY')
2025-06-11 01:42:38,055 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=270): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:42:38,055 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:42:39,064 - IBOptions.IBOptionsClient - ERROR - Error fetching market data: MarketData.__init__() got an unexpected keyword argument 'underlying_price'
2025-06-11 01:42:51,118 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:42:51,118 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=270): Can't find EId with tickerId:270
2025-06-11 01:42:52,071 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=274): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:42:52,071 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:42:59,359 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:42:59,360 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=274): Can't find EId with tickerId:274
2025-06-11 01:43:00,338 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=278): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:43:00,338 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:43:13,859 - IBOptions.IBOptionsClient - INFO - Placing order: BUY 1 SPY C 600.0 ********
2025-06-11 01:43:13,860 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=278): Can't find EId with tickerId:278
2025-06-11 01:43:14,090 - IBOptions.IBOptionsClient - INFO - Order placed successfully with ID: 280
2025-06-11 01:43:24,451 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 01:43:25,230 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=284): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:43:25,230 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:43:36,733 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=284): Can't find EId with tickerId:284
2025-06-11 01:43:36,953 - IBOptions.ConnectionManager - ERROR - IB Error 10168 (reqId=286): Requested market data is not subscribed. Delayed market data is not enabled.
2025-06-11 01:43:36,953 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=600.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00600000', tradingClass='SPY')
2025-06-11 01:43:38,178 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=288): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:43:38,178 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 01:43:39,188 - IBOptions.IBOptionsClient - ERROR - Error fetching market data: MarketData.__init__() got an unexpected keyword argument 'underlying_price'
2025-06-11 01:46:12,182 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:53:47,262 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:53:47,262 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:53:47,262 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:53:47,271 - IBOptions.ConnectionManager - ERROR - IB Error 326 (reqId=-1): Unable to connect as the client id is already in use. Retry with a unique client id.
2025-06-11 01:54:17,271 - IBOptions.ConnectionManager - ERROR - Connection attempt 1 failed: 
2025-06-11 01:54:17,271 - IBOptions.ConnectionManager - INFO - Retrying in 1.0 seconds...
2025-06-11 01:54:18,272 - IBOptions.ConnectionManager - INFO - Connection attempt 2/3
2025-06-11 01:54:18,272 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:54:18,280 - IBOptions.ConnectionManager - ERROR - IB Error 326 (reqId=-1): Unable to connect as the client id is already in use. Retry with a unique client id.
2025-06-11 01:54:30,009 - IBOptions.IBOptionsApp - INFO - Received signal 2, shutting down...
2025-06-11 01:54:30,010 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 01:54:30,010 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 01:54:30,010 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 01:54:30,010 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 01:54:30,010 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 01:54:30,010 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 01:58:53,441 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 01:58:53,441 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 01:58:53,441 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 01:58:53,459 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 01:58:53,459 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 01:58:53,459 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 01:58:53,460 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 01:58:53,460 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 01:58:53,460 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 01:58:53,460 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 01:58:53,460 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 01:58:53,889 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 01:58:53,889 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 01:58:53,889 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 01:58:56,121 - IBOptions.ConnectionManager - ERROR - IB Error 10168 (reqId=284): Requested market data is not subscribed. Delayed market data is not enabled.
2025-06-11 01:58:56,122 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=600.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00600000', tradingClass='SPY')
2025-06-11 01:58:57,131 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=284): Can't find EId with tickerId:284
2025-06-11 01:58:57,356 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=286): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:58:57,356 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=580.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00580000', tradingClass='SPY')
2025-06-11 01:58:57,656 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=286): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 580 Call/TOP/ALL
2025-06-11 01:58:57,656 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=580.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00580000', tradingClass='SPY')
2025-06-11 01:58:57,656 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=286): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 01:58:57,657 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=580.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00580000', tradingClass='SPY')
2025-06-11 01:58:58,374 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=286): Can't find EId with tickerId:286
2025-06-11 01:58:59,137 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=288): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:58:59,138 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=590.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00590000', tradingClass='SPY')
2025-06-11 01:58:59,378 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=288): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 590 Call/TOP/ALL
2025-06-11 01:58:59,378 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=590.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00590000', tradingClass='SPY')
2025-06-11 01:58:59,378 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=288): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 01:58:59,379 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=590.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00590000', tradingClass='SPY')
2025-06-11 01:59:00,134 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=288): Can't find EId with tickerId:288
2025-06-11 01:59:00,454 - IBOptions.ConnectionManager - ERROR - IB Error 10168 (reqId=290): Requested market data is not subscribed. Delayed market data is not enabled.
2025-06-11 01:59:00,454 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=600.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00600000', tradingClass='SPY')
2025-06-11 01:59:01,462 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=290): Can't find EId with tickerId:290
2025-06-11 01:59:02,225 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=292): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:59:02,225 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=610.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00610000', tradingClass='SPY')
2025-06-11 01:59:02,459 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=292): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 610 Call/TOP/ALL
2025-06-11 01:59:02,460 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=610.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00610000', tradingClass='SPY')
2025-06-11 01:59:02,461 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=292): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 01:59:02,461 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=610.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00610000', tradingClass='SPY')
2025-06-11 01:59:03,218 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=292): Can't find EId with tickerId:292
2025-06-11 01:59:03,975 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=294): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 01:59:03,975 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00620000', tradingClass='SPY')
2025-06-11 01:59:04,215 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=294): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 620 Call/TOP/ALL
2025-06-11 01:59:04,215 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00620000', tradingClass='SPY')
2025-06-11 01:59:04,216 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=294): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 01:59:04,216 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00620000', tradingClass='SPY')
2025-06-11 01:59:04,983 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 01:59:04,983 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 02:00:24,804 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 02:00:24,804 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 02:00:24,804 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 02:00:24,843 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 02:00:24,843 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 02:00:24,843 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 02:00:24,844 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 02:00:24,844 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 02:00:24,844 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 02:00:24,844 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 02:00:24,844 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 02:00:24,975 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 02:00:24,975 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 02:00:24,976 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 02:00:27,200 - IBOptions.ConnectionManager - ERROR - IB Error 10168 (reqId=284): Requested market data is not subscribed. Delayed market data is not enabled.
2025-06-11 02:00:27,200 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=600.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00600000', tradingClass='SPY')
2025-06-11 02:00:28,211 - IBOptions.IBOptionsClient - INFO - No valid market data received, using mock data
2025-06-11 02:00:28,211 - IBOptions.IBOptionsClient - INFO - Creating mock market data for SPY C 600.0 ********
2025-06-11 02:00:28,438 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=286): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:00:28,439 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=580.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00580000', tradingClass='SPY')
2025-06-11 02:00:28,675 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=286): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 580 Call/TOP/ALL
2025-06-11 02:00:28,675 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=580.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00580000', tradingClass='SPY')
2025-06-11 02:00:28,675 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=286): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:00:28,676 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=580.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00580000', tradingClass='SPY')
2025-06-11 02:00:29,430 - IBOptions.IBOptionsClient - INFO - No valid market data received, using mock data
2025-06-11 02:00:29,430 - IBOptions.IBOptionsClient - INFO - Creating mock market data for SPY C 580 ********
2025-06-11 02:00:29,654 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=288): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:00:29,655 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=590.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00590000', tradingClass='SPY')
2025-06-11 02:00:29,956 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=288): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 590 Call/TOP/ALL
2025-06-11 02:00:29,956 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=590.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00590000', tradingClass='SPY')
2025-06-11 02:00:29,957 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=288): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:00:29,958 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=590.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00590000', tradingClass='SPY')
2025-06-11 02:00:30,647 - IBOptions.IBOptionsClient - INFO - No valid market data received, using mock data
2025-06-11 02:00:30,647 - IBOptions.IBOptionsClient - INFO - Creating mock market data for SPY C 590 ********
2025-06-11 02:00:30,875 - IBOptions.ConnectionManager - ERROR - IB Error 10168 (reqId=290): Requested market data is not subscribed. Delayed market data is not enabled.
2025-06-11 02:00:30,876 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=600.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00600000', tradingClass='SPY')
2025-06-11 02:00:31,887 - IBOptions.IBOptionsClient - INFO - No valid market data received, using mock data
2025-06-11 02:00:31,887 - IBOptions.IBOptionsClient - INFO - Creating mock market data for SPY C 600 ********
2025-06-11 02:00:32,112 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=292): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:00:32,112 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=610.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00610000', tradingClass='SPY')
2025-06-11 02:00:32,373 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=292): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 610 Call/TOP/ALL
2025-06-11 02:00:32,373 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=610.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00610000', tradingClass='SPY')
2025-06-11 02:00:32,374 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=292): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:00:32,374 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=610.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00610000', tradingClass='SPY')
2025-06-11 02:00:33,111 - IBOptions.IBOptionsClient - INFO - No valid market data received, using mock data
2025-06-11 02:00:33,111 - IBOptions.IBOptionsClient - INFO - Creating mock market data for SPY C 610 ********
2025-06-11 02:00:33,335 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=294): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:00:33,335 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00620000', tradingClass='SPY')
2025-06-11 02:00:33,631 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=294): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 620 Call/TOP/ALL
2025-06-11 02:00:33,631 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00620000', tradingClass='SPY')
2025-06-11 02:00:33,631 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=294): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:00:33,632 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00620000', tradingClass='SPY')
2025-06-11 02:00:34,346 - IBOptions.IBOptionsClient - INFO - No valid market data received, using mock data
2025-06-11 02:00:34,346 - IBOptions.IBOptionsClient - INFO - Creating mock market data for SPY C 620 ********
2025-06-11 02:00:34,347 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 02:00:34,348 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 02:04:46,889 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 02:04:46,890 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 02:04:46,890 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 02:04:46,926 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 02:04:46,926 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 02:04:46,926 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 02:04:46,926 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 02:04:46,927 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 02:04:46,927 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 02:04:46,927 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 02:04:46,927 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 02:04:47,069 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 02:04:47,069 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 02:04:47,070 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 02:04:49,071 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 02:04:50,082 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=286): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:04:50,082 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 02:04:51,080 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 02:04:51,081 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 02:07:44,313 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 02:07:44,314 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 02:07:44,314 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 02:07:44,336 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 02:07:44,336 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 02:07:44,337 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 02:07:44,337 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 02:07:44,337 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 02:07:44,337 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 02:07:44,338 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 02:07:44,338 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 02:07:44,475 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 02:07:44,475 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 02:07:44,475 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 02:07:47,139 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=284): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:07:47,139 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=598.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00598000', tradingClass='SPY')
2025-06-11 02:07:47,439 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=284): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 10 '25 598 Call/TOP/ALL
2025-06-11 02:07:47,439 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=598.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00598000', tradingClass='SPY')
2025-06-11 02:07:47,439 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=284): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:07:47,440 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=598.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00598000', tradingClass='SPY')
2025-06-11 02:07:48,140 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=284): Can't find EId with tickerId:284
2025-06-11 02:07:48,897 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=286): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:07:48,897 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=588.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00588000', tradingClass='SPY')
2025-06-11 02:07:49,158 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=286): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 10 '25 588 Call/TOP/ALL
2025-06-11 02:07:49,158 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=588.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00588000', tradingClass='SPY')
2025-06-11 02:07:49,159 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=286): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:07:49,159 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=588.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00588000', tradingClass='SPY')
2025-06-11 02:07:49,920 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=286): Can't find EId with tickerId:286
2025-06-11 02:07:50,677 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=288): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:07:50,677 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=606.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00606000', tradingClass='SPY')
2025-06-11 02:07:50,924 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=288): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 11 '25 606 Call/TOP/ALL
2025-06-11 02:07:50,924 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=606.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00606000', tradingClass='SPY')
2025-06-11 02:07:50,924 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=288): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:07:50,924 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=606.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00606000', tradingClass='SPY')
2025-06-11 02:07:51,667 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=288): Can't find EId with tickerId:288
2025-06-11 02:07:52,428 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=290): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:07:52,429 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250612C00620000', tradingClass='SPY')
2025-06-11 02:07:52,730 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=290): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 12 '25 620 Call/TOP/ALL
2025-06-11 02:07:52,730 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250612C00620000', tradingClass='SPY')
2025-06-11 02:07:52,730 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=290): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:07:52,731 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250612C00620000', tradingClass='SPY')
2025-06-11 02:07:53,449 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 02:07:53,449 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 02:09:21,248 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 02:09:21,255 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 02:09:21,255 - IBOptions.Main - INFO - ============================================================
2025-06-11 02:09:21,255 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 02:09:21,255 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 02:09:21,256 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 02:09:21,256 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 02:09:21,256 - IBOptions.Main - INFO - ============================================================
2025-06-11 02:09:21,257 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 02:09:21,257 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 02:09:21,257 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 02:09:21,257 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 02:09:21,275 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 02:09:21,276 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 02:09:21,276 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 02:09:21,276 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 02:09:21,276 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 02:09:21,276 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 02:09:21,276 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 02:09:21,276 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 02:09:21,407 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 02:09:21,408 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 02:09:21,408 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 02:09:21,408 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-11 02:09:25,221 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 02:09:26,309 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=286): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:09:26,310 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 02:10:04,843 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=286): Can't find EId with tickerId:286
2025-06-11 02:10:05,065 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=288): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:10:05,066 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=598.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00598000', tradingClass='SPY')
2025-06-11 02:10:05,360 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=288): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.SPY JUN 10 '25 598 Call/TOP/ALL
2025-06-11 02:10:05,360 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=598.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00598000', tradingClass='SPY')
2025-06-11 02:10:05,360 - IBOptions.ConnectionManager - ERROR - IB Error 354 (reqId=288): Requested market data is not subscribed. Check API status by selecting the Account menu then under Management choose Market Data Subscription Manager and/or availability of delayed data.Delayed market data is available.Error&BEST/OPT/Top&BEST/OPT/Top
2025-06-11 02:10:05,361 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=598.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00598000', tradingClass='SPY')
2025-06-11 02:12:03,583 - IBOptions.IBOptionsApp - INFO - Received signal 2, shutting down...
2025-06-11 02:12:03,583 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 02:12:03,584 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 02:12:03,584 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 02:12:03,584 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 02:12:03,584 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 02:12:03,584 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 02:22:07,098 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 02:22:07,099 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 02:22:07,099 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 02:22:07,119 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 02:22:07,119 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 02:22:07,120 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 02:22:07,120 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 02:22:07,120 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 02:22:07,120 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 02:22:07,120 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 02:22:07,120 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 02:22:07,260 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 02:22:07,260 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 02:22:07,260 - IBOptions.ConnectionManager - INFO - Requested delayed market data subscription
2025-06-11 02:22:07,261 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 02:22:09,481 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=284): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:22:09,481 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=598.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00598000', tradingClass='SPY')
2025-06-11 02:22:09,724 - IBOptions.ConnectionManager - ERROR - IB Error 10167 (reqId=284): Requested market data is not subscribed. Displaying delayed market data.
2025-06-11 02:22:09,724 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=598.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00598000', tradingClass='SPY')
2025-06-11 02:22:12,704 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=286): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:22:12,704 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=588.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00588000', tradingClass='SPY')
2025-06-11 02:22:13,112 - IBOptions.ConnectionManager - ERROR - IB Error 10167 (reqId=286): Requested market data is not subscribed. Displaying delayed market data.
2025-06-11 02:22:13,112 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=588.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250610C00588000', tradingClass='SPY')
2025-06-11 02:22:16,029 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=288): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:22:16,029 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=606.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00606000', tradingClass='SPY')
2025-06-11 02:22:16,382 - IBOptions.ConnectionManager - ERROR - IB Error 10167 (reqId=288): Requested market data is not subscribed. Displaying delayed market data.
2025-06-11 02:22:16,382 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=606.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00606000', tradingClass='SPY')
2025-06-11 02:22:19,364 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=290): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:22:19,364 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250612C00620000', tradingClass='SPY')
2025-06-11 02:22:19,712 - IBOptions.ConnectionManager - ERROR - IB Error 10167 (reqId=290): Requested market data is not subscribed. Displaying delayed market data.
2025-06-11 02:22:19,712 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=620.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250612C00620000', tradingClass='SPY')
2025-06-11 02:22:22,376 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 02:22:22,376 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 02:25:46,265 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 02:25:46,268 - IBOptions.Main - INFO - IB Options Trading POC - Professional Edition
2025-06-11 02:25:46,269 - IBOptions.Main - INFO - ============================================================
2025-06-11 02:25:46,269 - IBOptions.Main - INFO - Phase 1: Python POC Development
2025-06-11 02:25:46,269 - IBOptions.Main - INFO - Objective: Develop a functional Python POC capable of all required functionality
2025-06-11 02:25:46,269 - IBOptions.Main - INFO - Core Tasks: API Connection & Authentication, Account Balances, Positions,
2025-06-11 02:25:46,269 - IBOptions.Main - INFO -            Option Chains, Market & Limit Order Placement, Order Status Tracking
2025-06-11 02:25:46,270 - IBOptions.Main - INFO - ============================================================
2025-06-11 02:25:46,271 - IBOptions.IBOptionsApp - INFO - Starting IB Options Trading POC
2025-06-11 02:25:46,271 - IBOptions.IBOptionsApp - INFO - Configuration: {'ib_connection': {'host': '127.0.0.1', 'port': 7497, 'client_id': 1, 'timeout': 30, 'max_retries': 3, 'retry_delay': 1.0}, 'trading': {'default_exchange': 'SMART', 'default_currency': 'USD', 'max_order_quantity': 1000, 'default_order_timeout': 300, 'enable_paper_trading': True, 'risk_check_enabled': True}, 'market_data': {'use_delayed_data': True, 'market_data_timeout': 10, 'max_concurrent_requests': 50, 'cache_duration': 60, 'enable_greeks': True}, 'logging': {'log_level': 'INFO', 'log_dir': 'logs', 'log_file_prefix': 'ib_options', 'max_log_files': 30, 'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s', 'enable_console_logging': True, 'enable_file_logging': True}, 'performance': {'enable_metrics': True, 'metrics_interval': 60, 'enable_benchmarking': False, 'max_memory_usage_mb': 512, 'enable_profiling': False}}
2025-06-11 02:25:46,271 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 02:25:46,271 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 02:25:46,290 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 02:25:46,291 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 02:25:46,291 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 02:25:46,291 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 02:25:46,292 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 02:25:46,292 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 02:25:46,292 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 02:25:46,292 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 02:25:46,423 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 02:25:46,423 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 02:25:46,424 - IBOptions.ConnectionManager - INFO - Requested delayed market data subscription
2025-06-11 02:25:46,424 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 02:25:46,425 - IBOptions.IBOptionsApp - INFO - Application started successfully
2025-06-11 02:25:51,523 - IBOptions.IBOptionsClient - INFO - Fetching option chain for SPY
2025-06-11 02:25:52,511 - IBOptions.ConnectionManager - ERROR - IB Error 10089 (reqId=286): Requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:25:52,511 - IBOptions.ConnectionManager - ERROR - Contract: Stock(conId=756733, symbol='SPY', exchange='SMART', primaryExchange='ARCA', currency='USD', localSymbol='SPY', tradingClass='SPY')
2025-06-11 02:26:04,590 - IBOptions.ConnectionManager - ERROR - IB Error 300 (reqId=286): Can't find EId with tickerId:286
2025-06-11 02:26:04,809 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=288): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:26:04,810 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=600.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00600000', tradingClass='SPY')
2025-06-11 02:39:10,412 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 02:39:15,945 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 02:39:15,945 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 02:39:15,950 - IBOptions.ConnectionManager - ERROR - IB Error 326 (reqId=-1): Unable to connect as the client id is already in use. Retry with a unique client id.
2025-06-11 02:39:26,078 - IBOptions.IBOptionsApp - INFO - Received signal 2, shutting down...
2025-06-11 02:39:26,078 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 02:39:26,079 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 02:39:26,079 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
2025-06-11 02:39:26,079 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 02:39:26,079 - IBOptions.IBOptionsApp - INFO - Shutting down application
2025-06-11 02:39:26,079 - IBOptions.IBOptionsApp - INFO - Application shutdown complete
2025-06-11 02:40:48,112 - IBOptions.PerformanceMonitor - INFO - Performance monitoring enabled
2025-06-11 02:40:48,114 - IBOptions.ConnectionManager - INFO - Connection attempt 1/3
2025-06-11 02:40:48,114 - IBOptions.ConnectionManager - INFO - Connecting to Interactive Brokers on 127.0.0.1:7497 (client_id=1)
2025-06-11 02:40:48,131 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm.nj
2025-06-11 02:40:48,131 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfuture
2025-06-11 02:40:48,132 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt.nj
2025-06-11 02:40:48,132 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:cashfarm
2025-06-11 02:40:48,132 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usopt
2025-06-11 02:40:48,132 - IBOptions.ConnectionManager - ERROR - IB Error 2104 (reqId=-1): Market data farm connection is OK:usfarm
2025-06-11 02:40:48,133 - IBOptions.ConnectionManager - ERROR - IB Error 2106 (reqId=-1): HMDS data farm connection is OK:ushmds
2025-06-11 02:40:48,133 - IBOptions.ConnectionManager - ERROR - IB Error 2158 (reqId=-1): Sec-def data farm connection is OK:secdefnj
2025-06-11 02:40:48,274 - IBOptions.ConnectionManager - INFO - Connection established
2025-06-11 02:40:48,274 - IBOptions.ConnectionManager - INFO - Successfully connected to Interactive Brokers
2025-06-11 02:40:48,275 - IBOptions.ConnectionManager - INFO - Requested delayed market data subscription
2025-06-11 02:40:48,275 - IBOptions.IBOptionsClient - INFO - Client connected successfully
2025-06-11 02:40:50,916 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=284): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:40:50,916 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=787911451, symbol='SPY', lastTradeDateOrContractMonth='********', strike=595.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00595000', tradingClass='SPY')
2025-06-11 02:40:51,190 - IBOptions.ConnectionManager - ERROR - IB Error 10167 (reqId=284): Requested market data is not subscribed. Displaying delayed market data.
2025-06-11 02:40:51,190 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=787911451, symbol='SPY', lastTradeDateOrContractMonth='********', strike=595.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00595000', tradingClass='SPY')
2025-06-11 02:40:54,437 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=286): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:40:54,437 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=*********, symbol='SPY', lastTradeDateOrContractMonth='********', strike=600.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00600000', tradingClass='SPY')
2025-06-11 02:40:57,992 - IBOptions.ConnectionManager - ERROR - IB Error 10091 (reqId=288): Part of requested market data requires additional subscription for API. See link in 'Market Data Connections' dialog for more details.SPY ARCA/TOP/ALL
2025-06-11 02:40:57,992 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=787911489, symbol='SPY', lastTradeDateOrContractMonth='********', strike=605.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00605000', tradingClass='SPY')
2025-06-11 02:40:58,209 - IBOptions.ConnectionManager - ERROR - IB Error 10167 (reqId=288): Requested market data is not subscribed. Displaying delayed market data.
2025-06-11 02:40:58,209 - IBOptions.ConnectionManager - ERROR - Contract: Option(conId=787911489, symbol='SPY', lastTradeDateOrContractMonth='********', strike=605.0, right='C', multiplier='100', exchange='SMART', currency='USD', localSymbol='SPY   250611C00605000', tradingClass='SPY')
2025-06-11 02:41:05,040 - IBOptions.ConnectionManager - WARNING - Connection lost
2025-06-11 02:41:05,040 - IBOptions.IBOptionsClient - INFO - Client disconnected successfully
