#!/usr/bin/env python3
"""
Real-time streaming option chain market data
"""

import time
import threading
from datetime import datetime
from ib_options import IBOptionsClient, OptionContract, OptionRight
import os

class OptionChainStreamer:
    def __init__(self):
        self.client = IBOptionsClient()
        self.streaming = False
        self.tickers = {}
        self.update_count = 0
        
    def stream_option_chain(self, symbol: str, expiry: str, strikes: list, duration: int = 5):
        """Stream market data for multiple strikes in an option chain"""
        print(f"🔴 LIVE STREAMING: {symbol} Option Chain - {expiry}")
        print(f"⏱️  Duration: {duration} seconds")
        print(f"📊 Strikes: {strikes}")
        print("=" * 80)
        
        try:
            # Connect to IB
            print("🔌 Connecting to IB...")
            self.client.connect()
            time.sleep(2)
            
            if not self.client.is_connected():
                print("❌ Failed to connect to IB")
                return
            
            # Create option contracts for each strike
            contracts = []
            for strike in strikes:
                # Create both calls and puts
                call_contract = OptionContract(
                    symbol=symbol,
                    expiry=expiry,
                    strike=strike,
                    right=OptionRight.CALL
                )
                put_contract = OptionContract(
                    symbol=symbol,
                    expiry=expiry,
                    strike=strike,
                    right=OptionRight.PUT
                )
                contracts.extend([call_contract, put_contract])
            
            # Start streaming for all contracts
            print(f"📡 Starting stream for {len(contracts)} contracts...")
            
            for contract in contracts:
                try:
                    # Qualify the contract
                    ib_contract = self.client._create_ib_contract(contract)
                    qualified = self.client.ib.qualifyContracts(ib_contract)
                    
                    if qualified:
                        # Request streaming market data
                        ticker = self.client.ib.reqMktData(qualified[0], '', False, False)
                        key = f"{contract.symbol}_{contract.strike}_{contract.right.value}"
                        self.tickers[key] = {
                            'ticker': ticker,
                            'contract': contract,
                            'last_update': time.time()
                        }
                        time.sleep(0.1)  # Small delay between requests
                except Exception as e:
                    print(f"⚠️  Failed to start stream for {contract.symbol} {contract.strike} {contract.right.value}: {e}")
            
            print(f"✅ Started streaming for {len(self.tickers)} contracts")
            print("\n🔴 LIVE DATA STREAM:")
            print("=" * 80)
            
            # Stream for specified duration
            self.streaming = True
            start_time = time.time()
            last_display = 0
            
            while time.time() - start_time < duration and self.streaming:
                current_time = time.time()
                
                # Update display every 0.5 seconds
                if current_time - last_display >= 0.5:
                    self._display_live_data()
                    last_display = current_time
                
                # Process IB events
                self.client.ib.sleep(0.1)
            
            print(f"\n⏹️  Stream ended after {duration} seconds")
            print(f"📊 Total updates received: {self.update_count}")
            
        except Exception as e:
            print(f"❌ Error during streaming: {e}")
        finally:
            # Clean up
            self._cleanup()
    
    def _display_live_data(self):
        """Display current market data in a table format"""
        # Clear screen (works on most terminals)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print(f"🔴 LIVE OPTION CHAIN DATA - {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 100)
        print(f"{'Strike':<8} {'Type':<4} {'Bid':<8} {'Ask':<8} {'Last':<8} {'Vol':<10} {'IV':<8} {'Delta':<8}")
        print("-" * 100)
        
        # Sort by strike price
        sorted_items = sorted(self.tickers.items(), key=lambda x: (x[1]['contract'].strike, x[1]['contract'].right.value))
        
        for key, data in sorted_items:
            ticker = data['ticker']
            contract = data['contract']
            
            # Extract current values
            bid = getattr(ticker, 'bid', 0) or 0
            ask = getattr(ticker, 'ask', 0) or 0
            last = getattr(ticker, 'last', 0) or 0
            volume = getattr(ticker, 'volume', 0) or 0
            iv = getattr(ticker, 'impliedVolatility', 0) or 0
            delta = getattr(ticker, 'delta', 0) or 0
            
            # Format values
            bid_str = f"${bid:.2f}" if bid > 0 else "-"
            ask_str = f"${ask:.2f}" if ask > 0 else "-"
            last_str = f"${last:.2f}" if last > 0 else "-"
            vol_str = f"{int(volume):,}" if volume > 0 else "-"
            iv_str = f"{iv:.3f}" if iv > 0 else "-"
            delta_str = f"{delta:.3f}" if delta != 0 else "-"
            
            print(f"{contract.strike:<8.0f} {contract.right.value:<4} {bid_str:<8} {ask_str:<8} {last_str:<8} {vol_str:<10} {iv_str:<8} {delta_str:<8}")
        
        print("-" * 100)
        print(f"📊 Active streams: {len(self.tickers)} | Updates: {self.update_count}")
        print("Press Ctrl+C to stop streaming early")
    
    def _cleanup(self):
        """Clean up streaming connections"""
        print("\n🧹 Cleaning up streams...")
        
        for key, data in self.tickers.items():
            try:
                # Cancel market data subscription
                ticker = data['ticker']
                self.client.ib.cancelMktData(ticker.contract)
            except Exception as e:
                pass
        
        self.tickers.clear()
        self.streaming = False
        
        try:
            self.client.disconnect()
        except Exception:
            pass
        
        print("✅ Cleanup complete")

def main():
    print("🚀 OPTION CHAIN STREAMING TOOL")
    print("=" * 50)
    
    # Get user input
    symbol = input("Enter symbol (e.g., SPY): ").upper().strip()
    expiry = input("Enter expiration (YYYYMMDD, e.g., 20250611): ").strip()
    
    print("\nSelect strikes to stream:")
    print("1. Around-the-money (5 strikes)")
    print("2. Wide range (10 strikes)")
    print("3. Custom strikes")
    
    choice = input("Choice (1-3): ").strip()
    
    if choice == "1":
        # Around-the-money strikes for SPY
        if symbol == "SPY":
            strikes = [590, 595, 600, 605, 610]
        else:
            strikes = [95, 100, 105, 110, 115]  # Generic
    elif choice == "2":
        # Wide range
        if symbol == "SPY":
            strikes = [580, 585, 590, 595, 600, 605, 610, 615, 620, 625]
        else:
            strikes = [90, 95, 100, 105, 110, 115, 120, 125, 130, 135]
    else:
        # Custom strikes
        strikes_input = input("Enter strikes separated by commas (e.g., 590,600,610): ")
        strikes = [float(s.strip()) for s in strikes_input.split(",")]
    
    duration = int(input("Stream duration in seconds (default 5): ") or "5")
    
    # Start streaming
    streamer = OptionChainStreamer()
    try:
        streamer.stream_option_chain(symbol, expiry, strikes, duration)
    except KeyboardInterrupt:
        print("\n⏹️  Streaming stopped by user")
        streamer.streaming = False
        streamer._cleanup()

if __name__ == "__main__":
    main()
