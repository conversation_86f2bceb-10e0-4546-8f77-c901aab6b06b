#!/usr/bin/env python3
"""
Quick script to check available SPY option strikes and help with market data
"""

from ib_options import IBOptionsClient, OptionContract, OptionRight
import time

def main():
    print("🔍 Checking SPY Option Chain...")
    
    # Create client and connect with different client ID
    client = IBOptionsClient()
    try:
        client.connect(client_id=2)  # Use different client ID
        time.sleep(2)
        
        # Get SPY option chain
        print("📊 Fetching SPY option chain...")
        chain = client.get_option_chain('SPY')
        
        if not chain:
            print("❌ Failed to get option chain")
            return
        
        print(f"✅ SPY Option Chain Retrieved")
        print(f"   Underlying Price: ${chain.underlying_price:.2f}" if chain.underlying_price > 0 else "   Underlying Price: Not available")
        print(f"   Exchange: {chain.exchange}")
        print(f"   Total Expirations: {len(chain.expirations)}")
        print(f"   Total Strikes: {len(chain.strikes)}")
        
        # Show available expirations
        print(f"\n📅 Next 5 Expirations:")
        for i, exp in enumerate(sorted(chain.expirations)[:5]):
            print(f"   {i+1}. {exp}")
        
        # Find reasonable strikes (around $600 since that's where SPY trades)
        current_estimate = 600  # SPY typically trades around $600
        reasonable_strikes = [s for s in sorted(chain.strikes) if 550 <= s <= 650]
        
        print(f"\n💰 Reasonable Strikes (550-650 range):")
        for i, strike in enumerate(reasonable_strikes[:10]):
            print(f"   {i+1}. ${strike}")
        
        # Try to get market data for a realistic option
        if reasonable_strikes and chain.expirations:
            print(f"\n🧪 Testing Market Data for Realistic Option...")
            
            # Use a middle strike and nearest expiry
            test_strike = reasonable_strikes[len(reasonable_strikes)//2]
            test_expiry = sorted(chain.expirations)[0]
            
            print(f"   Testing: SPY ${test_strike} Call {test_expiry}")
            
            contract = OptionContract(
                symbol='SPY',
                expiry=test_expiry,
                strike=test_strike,
                right=OptionRight.CALL
            )
            
            market_data = client.get_option_market_data(contract)
            if market_data:
                print("   ✅ Market data retrieved successfully!")
                print(f"   📈 Bid: ${market_data.bid:.2f}")
                print(f"   📈 Ask: ${market_data.ask:.2f}")
                print(f"   📈 Last: ${market_data.last:.2f}")
                print(f"   📊 Volume: {market_data.volume}")
            else:
                print("   ❌ Failed to get market data (likely subscription issue)")
        
        print(f"\n💡 RECOMMENDATION:")
        print(f"   Use strikes between $550-$650")
        print(f"   Use expiration: {sorted(chain.expirations)[0]} (nearest)")
        print(f"   Example: SPY $600 Call {sorted(chain.expirations)[0]}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        client.disconnect()
        print("\n🔌 Disconnected from IB")

if __name__ == "__main__":
    main()
