"""
Test suite for IBOptionsClient
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from ib_options.client import IBOptionsClient
from ib_options.models import OptionContract, OptionRight, Order, OrderType, OrderAction
from ib_options.config import config

class TestIBOptionsClient:
    """Test cases for IBOptionsClient"""
    
    @pytest.fixture
    def client(self):
        """Create a test client"""
        return IBOptionsClient()
    
    @pytest.fixture
    def mock_ib(self):
        """Create a mock IB connection"""
        mock = Mock()
        mock.isConnected.return_value = True
        return mock
    
    def test_client_initialization(self, client):
        """Test client initialization"""
        assert client is not None
        assert client.ib is None
        assert client._market_data_cache == {}
        assert client.logger is not None
    
    @patch('ib_options.client.connection_manager')
    def test_connect_success(self, mock_connection_manager, client, mock_ib):
        """Test successful connection"""
        mock_connection_manager.connect.return_value = mock_ib
        
        result = client.connect()
        
        assert result is True
        assert client.ib == mock_ib
        mock_connection_manager.connect.assert_called_once()
    
    @patch('ib_options.client.connection_manager')
    def test_connect_failure(self, mock_connection_manager, client):
        """Test connection failure"""
        mock_connection_manager.connect.return_value = None
        
        result = client.connect()
        
        assert result is False
        assert client.ib is None
    
    def test_is_connected_true(self, client, mock_ib):
        """Test is_connected when connected"""
        client.ib = mock_ib
        
        result = client.is_connected()
        
        assert result is True
        mock_ib.isConnected.assert_called_once()
    
    def test_is_connected_false(self, client):
        """Test is_connected when not connected"""
        result = client.is_connected()
        
        assert result is False
    
    def test_get_account_balances_not_connected(self, client):
        """Test get_account_balances when not connected"""
        result = client.get_account_balances()
        
        assert result is None
    
    def test_get_account_balances_success(self, client, mock_ib):
        """Test successful account balances retrieval"""
        client.ib = mock_ib
        
        # Mock account summary data
        mock_account_values = [
            Mock(tag='AvailableFunds', value='10000.00'),
            Mock(tag='BuyingPower', value='20000.00'),
            Mock(tag='NetLiquidation', value='15000.00'),
            Mock(tag='OptionMarketValue', value='5000.00'),
        ]
        mock_ib.accountSummary.return_value = mock_account_values
        
        result = client.get_account_balances()
        
        assert result is not None
        assert result.available_funds == 10000.00
        assert result.buying_power == 20000.00
        assert result.net_liquidation == 15000.00
        assert result.option_market_value == 5000.00
    
    def test_get_option_positions_not_connected(self, client):
        """Test get_option_positions when not connected"""
        result = client.get_option_positions()
        
        assert result == []
    
    def test_get_option_positions_success(self, client, mock_ib):
        """Test successful option positions retrieval"""
        client.ib = mock_ib
        
        # Mock position data
        mock_contract = Mock()
        mock_contract.secType = 'OPT'
        mock_contract.symbol = 'SPY'
        mock_contract.lastTradeDateOrContractMonth = '********'
        mock_contract.strike = 450.0
        mock_contract.right = 'C'
        mock_contract.exchange = 'SMART'
        mock_contract.currency = 'USD'
        mock_contract.multiplier = 100
        mock_contract.tradingClass = 'SPY'
        mock_contract.conId = 12345
        mock_contract.localSymbol = 'SPY   241220C00450000'
        
        mock_position = Mock()
        mock_position.contract = mock_contract
        mock_position.position = 10
        mock_position.avgCost = 5.50
        
        mock_ib.positions.return_value = [mock_position]
        
        # Mock market data call to return None (no market data)
        with patch.object(client, 'get_option_market_data', return_value=None):
            result = client.get_option_positions()
        
        assert len(result) == 1
        position = result[0]
        assert position.contract.symbol == 'SPY'
        assert position.contract.strike == 450.0
        assert position.contract.right == OptionRight.CALL
        assert position.position == 10
        assert position.avg_cost == 5.50
    
    def test_get_option_chain_not_connected(self, client):
        """Test get_option_chain when not connected"""
        result = client.get_option_chain('SPY')
        
        assert result is None
    
    def test_get_option_market_data_not_connected(self, client):
        """Test get_option_market_data when not connected"""
        contract = OptionContract(
            symbol='SPY',
            expiry='********',
            strike=450.0,
            right=OptionRight.CALL
        )
        
        result = client.get_option_market_data(contract)
        
        assert result is None
    
    def test_place_option_order_not_connected(self, client):
        """Test place_option_order when not connected"""
        contract = OptionContract(
            symbol='SPY',
            expiry='********',
            strike=450.0,
            right=OptionRight.CALL
        )
        
        order = Order(
            contract=contract,
            action=OrderAction.BUY,
            quantity=1,
            order_type=OrderType.MARKET
        )
        
        result = client.place_option_order(order)
        
        assert result is None
    
    def test_get_order_status_not_connected(self, client):
        """Test get_order_status when not connected"""
        result = client.get_order_status(123)
        
        assert result is None
    
    def test_cancel_order_not_connected(self, client):
        """Test cancel_order when not connected"""
        result = client.cancel_order(123)
        
        assert result is False
    
    def test_get_available_symbols(self, client):
        """Test get_available_symbols"""
        result = client.get_available_symbols()
        
        assert isinstance(result, list)
        assert len(result) > 0
        assert 'SPY' in result
        assert 'QQQ' in result
    
    def test_clear_cache(self, client):
        """Test cache clearing"""
        # Add some dummy data to cache
        client._market_data_cache['test_key'] = Mock()
        
        assert len(client._market_data_cache) == 1
        
        client.clear_cache()
        
        assert len(client._market_data_cache) == 0
    
    def test_get_cache_stats(self, client):
        """Test cache statistics"""
        # Add some dummy data to cache
        client._market_data_cache['test_key1'] = Mock()
        client._market_data_cache['test_key2'] = Mock()
        
        stats = client.get_cache_stats()
        
        assert stats['cache_size'] == 2
        assert 'test_key1' in stats['cache_keys']
        assert 'test_key2' in stats['cache_keys']

class TestIntegration:
    """Integration tests (require actual IB connection)"""
    
    @pytest.mark.integration
    def test_full_workflow(self):
        """Test full workflow with real IB connection"""
        client = IBOptionsClient()
        
        # This test requires actual IB connection
        # Skip if not available
        if not client.connect():
            pytest.skip("IB connection not available")
        
        try:
            # Test account balances
            balances = client.get_account_balances()
            assert balances is not None
            
            # Test option chain
            chain = client.get_option_chain('SPY')
            assert chain is not None
            assert len(chain.expirations) > 0
            assert len(chain.strikes) > 0
            
            # Test market data
            if chain.expirations and chain.strikes:
                contract = OptionContract(
                    symbol='SPY',
                    expiry=chain.expirations[0],
                    strike=chain.strikes[len(chain.strikes)//2],
                    right=OptionRight.CALL
                )
                
                market_data = client.get_option_market_data(contract)
                # Market data might be None if market is closed or no subscription
                # Just verify no exception is raised
                
        finally:
            client.disconnect()

if __name__ == "__main__":
    pytest.main([__file__])
