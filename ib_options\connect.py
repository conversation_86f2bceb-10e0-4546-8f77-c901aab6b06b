from ib_insync import *
import logging
import sys
from datetime import datetime

def setup_logger():
    """Configure and return a logger for the application"""
    logger = logging.getLogger('IBOptions')
    logger.setLevel(logging.DEBUG)
    fh = logging.FileHandler(f'logs/ib_options_{datetime.now().strftime("%Y%m%d")}.log')
    fh.setLevel(logging.DEBUG)
    
    ch = logging.StreamHandler(sys.stdout)
    ch.setLevel(logging.INFO)
 
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)
    
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    return logger

def connect_ib(port=7497, client_id=1, host='127.0.0.1'):

    logger = setup_logger()
    
    try:
        ib = IB()
        
        logger.info(f"Attempting to connect to Interactive Brokers on {host}:{port}...")
        ib.connect(host, port, clientId=client_id)
        
        if ib.isConnected():
            logger.info("Successfully connected to Interactive Brokers")
            return ib
        else:
            logger.error("Failed to connect to Interactive Brokers")
            return None
            
    except Exception as e:
        logger.error(f"Error connecting to Interactive Brokers: {str(e)}")
        return None