from ib_insync import *
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional
from .config import config

class IBOptionsLogger:
    """Enhanced logger for IB Options Trading POC"""

    _instance = None
    _logger = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if self._logger is None:
            self._setup_logger()

    def _setup_logger(self):
        """Setup logger with configuration"""
        self._logger = logging.getLogger('IBOptions')
        self._logger.setLevel(getattr(logging, config.logging.log_level))

        # Clear existing handlers
        self._logger.handlers.clear()

        # Create formatter
        formatter = logging.Formatter(config.logging.log_format)

        # Console handler
        if config.logging.enable_console_logging:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, config.logging.log_level))
            console_handler.setFormatter(formatter)
            self._logger.addHandler(console_handler)

        # File handler
        if config.logging.enable_file_logging:
            # Ensure log directory exists
            log_dir = Path(config.logging.log_dir)
            log_dir.mkdir(parents=True, exist_ok=True)

            log_file = log_dir / f"{config.logging.log_file_prefix}_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            self._logger.addHandler(file_handler)

            # Cleanup old log files
            self._cleanup_old_logs(log_dir)

    def _cleanup_old_logs(self, log_dir: Path):
        """Remove old log files"""
        try:
            log_files = list(log_dir.glob(f"{config.logging.log_file_prefix}_*.log"))
            if len(log_files) > config.logging.max_log_files:
                # Sort by modification time and remove oldest
                log_files.sort(key=lambda x: x.stat().st_mtime)
                for old_file in log_files[:-config.logging.max_log_files]:
                    old_file.unlink()
        except Exception as e:
            self._logger.warning(f"Failed to cleanup old log files: {e}")

    def get_logger(self, name: Optional[str] = None) -> logging.Logger:
        """Get logger instance"""
        if name:
            return logging.getLogger(f'IBOptions.{name}')
        return self._logger

def setup_logger(name: Optional[str] = None) -> logging.Logger:
    """Setup and return logger instance"""
    logger_manager = IBOptionsLogger()
    return logger_manager.get_logger(name)

class IBConnectionManager:
    """Enhanced connection manager for Interactive Brokers"""

    def __init__(self):
        self.logger = setup_logger('ConnectionManager')
        self.ib = None
        self._connection_callbacks = []
        self._disconnection_callbacks = []

    def connect(self, host: Optional[str] = None, port: Optional[int] = None,
                client_id: Optional[int] = None, timeout: Optional[int] = None) -> Optional[IB]:
        """Connect to Interactive Brokers with retry logic"""
        # Use config defaults if not provided
        host = host or config.ib_connection.host
        port = port or config.ib_connection.port
        client_id = client_id or config.ib_connection.client_id
        timeout = timeout or config.ib_connection.timeout

        for attempt in range(config.ib_connection.max_retries):
            try:
                self.logger.info(f"Connection attempt {attempt + 1}/{config.ib_connection.max_retries}")
                self.logger.info(f"Connecting to Interactive Brokers on {host}:{port} (client_id={client_id})")

                self.ib = IB()

                # Set up event handlers
                self.ib.connectedEvent += self._on_connected
                self.ib.disconnectedEvent += self._on_disconnected
                self.ib.errorEvent += self._on_error

                # Connect with timeout
                self.ib.connect(host, port, clientId=client_id, timeout=timeout)

                if self.ib.isConnected():
                    self.logger.info("Successfully connected to Interactive Brokers")
                    self._trigger_connection_callbacks()
                    return self.ib
                else:
                    self.logger.error("Failed to connect to Interactive Brokers")

            except Exception as e:
                self.logger.error(f"Connection attempt {attempt + 1} failed: {str(e)}")
                if self.ib:
                    try:
                        self.ib.disconnect()
                    except:
                        pass
                    self.ib = None

                if attempt < config.ib_connection.max_retries - 1:
                    self.logger.info(f"Retrying in {config.ib_connection.retry_delay} seconds...")
                    import time
                    time.sleep(config.ib_connection.retry_delay)

        self.logger.error("All connection attempts failed")
        return None

    def disconnect(self):
        """Disconnect from Interactive Brokers"""
        if self.ib and self.ib.isConnected():
            try:
                self.logger.info("Disconnecting from Interactive Brokers")
                self.ib.disconnect()
                self._trigger_disconnection_callbacks()
            except Exception as e:
                self.logger.error(f"Error during disconnection: {e}")
        self.ib = None

    def is_connected(self) -> bool:
        """Check if connected to Interactive Brokers"""
        return self.ib is not None and self.ib.isConnected()

    def get_connection(self) -> Optional[IB]:
        """Get the current IB connection"""
        return self.ib if self.is_connected() else None

    def add_connection_callback(self, callback):
        """Add callback for connection events"""
        self._connection_callbacks.append(callback)

    def add_disconnection_callback(self, callback):
        """Add callback for disconnection events"""
        self._disconnection_callbacks.append(callback)

    def _on_connected(self):
        """Handle connection event"""
        self.logger.info("Connection established")

    def _on_disconnected(self):
        """Handle disconnection event"""
        self.logger.warning("Connection lost")

    def _on_error(self, reqId, errorCode, errorString, contract):
        """Handle error events"""
        self.logger.error(f"IB Error {errorCode} (reqId={reqId}): {errorString}")
        if contract:
            self.logger.error(f"Contract: {contract}")

    def _trigger_connection_callbacks(self):
        """Trigger connection callbacks"""
        for callback in self._connection_callbacks:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"Error in connection callback: {e}")

    def _trigger_disconnection_callbacks(self):
        """Trigger disconnection callbacks"""
        for callback in self._disconnection_callbacks:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"Error in disconnection callback: {e}")

# Global connection manager instance
connection_manager = IBConnectionManager()

def connect_ib(port=None, client_id=None, host=None, timeout=None) -> Optional[IB]:
    """Connect to Interactive Brokers (backward compatibility)"""
    return connection_manager.connect(host, port, client_id, timeout)

def get_ib_connection() -> Optional[IB]:
    """Get current IB connection"""
    return connection_manager.get_connection()