from ib_insync import Stock, Option
from ib_options.connect import setup_logger
import math
import time

def get_option_chain(ib, symbol, exchange='SMART'):
    """Fetch option chain for a given underlying symbol
    
    Args:
        ib: IB connection object
        symbol: Underlying symbol (e.g., 'SPY')
        exchange: Exchange (default: 'SMART')
        
    Returns:
        Option chain data or None if error occurs
    """
    logger = setup_logger()
    try:
        # Create contract for underlying
        contract = Stock(symbol, exchange, 'USD')
        
        # Request contract details to ensure we have the correct contract
        ib.qualifyContracts(contract)
        
        # Request option chain
        chains = ib.reqSecDefOptParams(contract.symbol, '', contract.secType, contract.conId)
        
        if not chains:
            logger.warning(f"No option chains found for {symbol}")
            return None
            
        logger.info(f"Retrieved option chain for {symbol}")
        return chains
    except Exception as e:
        logger.error(f"Error fetching option chain for {symbol}: {str(e)}")
        return None

def get_option_market_data(ib, symbol, expiry, strike, right, exchange='SMART', use_delayed=True):
    """Get market data for a specific option contract

    Args:
        ib: IB connection object
        symbol: Underlying symbol (e.g., 'SPY')
        expiry: Expiration date in format YYYYMMDD
        strike: Strike price
        right: 'C' for Call, 'P' for Put
        exchange: Exchange (default: 'SMART')
        use_delayed: Whether to request delayed data (default: True, free)

    Returns:
        Dictionary with market data or None if error occurs
    """
    logger = setup_logger()
    try:
        # Create option contract
        contract = Option(symbol, expiry, strike, right, exchange)

        # Qualify the contract
        qualified_contracts = ib.qualifyContracts(contract)
        if not qualified_contracts:
            logger.error(f"Could not qualify option contract: {symbol} {expiry} {strike} {right}")
            return None

        contract = qualified_contracts[0]
        logger.info(f"Qualified contract: {contract}")

        # Cancel any existing market data requests for this contract
        try:
            ib.cancelMktData(contract)
            ib.sleep(0.1)
        except:
            pass

        # Request market data with appropriate flags
        # For delayed data (free): use snapshot=True and regulatorySnapshot=False
        if use_delayed:
            logger.info("Requesting delayed market data (free)...")
            ticker = ib.reqMktData(contract, '', True, False)  # snapshot=True for delayed data
        else:
            logger.info("Requesting real-time market data...")
            ticker = ib.reqMktData(contract, '', False, False)

        # Wait longer for data to arrive and check multiple times
        max_wait_time = 5  # seconds
        wait_interval = 0.5
        waited = 0

        while waited < max_wait_time:
            ib.sleep(wait_interval)
            waited += wait_interval

            # Check if we have any meaningful data
            if (hasattr(ticker, 'bid') and ticker.bid > 0) or \
               (hasattr(ticker, 'ask') and ticker.ask > 0) or \
               (hasattr(ticker, 'last') and ticker.last > 0):
                break

        # Helper function to safely get numeric values
        def safe_get_value(obj, attr, default=0):
            try:
                value = getattr(obj, attr, default)
                if value is None or math.isnan(value) or math.isinf(value):
                    return default
                return value
            except (TypeError, ValueError):
                return default

        # Compile market data - handle missing attributes safely
        market_data = {
            'symbol': symbol,
            'expiry': expiry,
            'strike': strike,
            'right': right,
            'contract_id': getattr(contract, 'conId', 'N/A'),
            'local_symbol': getattr(contract, 'localSymbol', 'N/A'),
            'bid': safe_get_value(ticker, 'bid'),
            'ask': safe_get_value(ticker, 'ask'),
            'last': safe_get_value(ticker, 'last'),
            'close': safe_get_value(ticker, 'close'),
            'volume': safe_get_value(ticker, 'volume'),
            'open_interest': safe_get_value(ticker, 'openInterest'),
            'implied_volatility': safe_get_value(ticker, 'impliedVolatility'),
            'delta': safe_get_value(ticker, 'delta'),
            'gamma': safe_get_value(ticker, 'gamma'),
            'theta': safe_get_value(ticker, 'theta'),
            'vega': safe_get_value(ticker, 'vega'),
            'data_type': 'delayed' if use_delayed else 'real-time'
        }

        # Calculate mid price if bid and ask are available
        if market_data['bid'] > 0 and market_data['ask'] > 0:
            market_data['mid'] = (market_data['bid'] + market_data['ask']) / 2
        else:
            market_data['mid'] = 0

        # Log the results
        if market_data['bid'] > 0 or market_data['ask'] > 0 or market_data['last'] > 0:
            logger.info(f"Successfully retrieved market data for {symbol} {right} {strike} {expiry}")
            logger.info(f"Bid: {market_data['bid']}, Ask: {market_data['ask']}, Last: {market_data['last']}")
        else:
            logger.warning(f"No market data available for {symbol} {right} {strike} {expiry}")
            logger.warning("This might be due to: 1) Contract not actively traded, 2) Market closed, 3) Invalid contract")

        # Clean up - cancel the market data request
        try:
            ib.cancelMktData(contract)
        except:
            pass

        return market_data

    except Exception as e:
        logger.error(f"Error fetching option market data: {str(e)}")
        # Try to cancel any pending requests
        try:
            ib.cancelMktData(contract)
        except:
            pass
        return None

def get_option_chain_with_prices(ib, symbol, expiry=None, max_strikes=10, exchange='SMART'):
    """Get option chain with market data for a specific expiration

    Args:
        ib: IB connection object
        symbol: Underlying symbol (e.g., 'SPY')
        expiry: Specific expiration date (YYYYMMDD) or None for nearest
        max_strikes: Maximum number of strikes to retrieve (default: 10)
        exchange: Exchange (default: 'SMART')

    Returns:
        Dictionary with option chain and market data or None if error occurs
    """
    logger = setup_logger()
    try:
        # First get the option chain
        chains = get_option_chain(ib, symbol, exchange)
        if not chains:
            logger.error(f"No option chains found for {symbol}")
            return None

        chain = next(iter(chains))

        # Select expiration
        if expiry is None:
            # Use the nearest expiration
            expiry = sorted(chain.expirations)[0]
            logger.info(f"Using nearest expiration: {expiry}")
        elif expiry not in chain.expirations:
            logger.error(f"Expiration {expiry} not found in chain")
            return None

        # Get current stock price to find ATM strikes
        try:
            stock = Stock(symbol, exchange, 'USD')
            ib.qualifyContracts(stock)
            stock_ticker = ib.reqMktData(stock, '', True, False)  # delayed data
            ib.sleep(1)

            current_price = getattr(stock_ticker, 'last', 0)
            if current_price <= 0:
                current_price = getattr(stock_ticker, 'close', 0)
            if current_price <= 0:
                # Use middle strike as fallback
                current_price = sorted(chain.strikes)[len(chain.strikes)//2]

            ib.cancelMktData(stock)
            logger.info(f"Current stock price: {current_price}")
        except Exception as e:
            logger.warning(f"Could not get stock price: {e}")
            current_price = sorted(chain.strikes)[len(chain.strikes)//2]

        # Find strikes around current price
        strikes = sorted(chain.strikes)
        atm_index = min(range(len(strikes)), key=lambda i: abs(strikes[i] - current_price))

        # Select strikes around ATM
        start_idx = max(0, atm_index - max_strikes//2)
        end_idx = min(len(strikes), start_idx + max_strikes)
        selected_strikes = strikes[start_idx:end_idx]

        logger.info(f"Selected {len(selected_strikes)} strikes around ATM: {selected_strikes}")

        # Get market data for calls and puts
        option_data = {
            'symbol': symbol,
            'expiry': expiry,
            'current_price': current_price,
            'strikes': [],
            'calls': [],
            'puts': []
        }

        for strike in selected_strikes:
            logger.info(f"Getting market data for strike {strike}...")

            # Get call data
            call_data = get_option_market_data(ib, symbol, expiry, strike, 'C', exchange, use_delayed=True)
            if call_data:
                option_data['calls'].append(call_data)

            # Get put data
            put_data = get_option_market_data(ib, symbol, expiry, strike, 'P', exchange, use_delayed=True)
            if put_data:
                option_data['puts'].append(put_data)

            option_data['strikes'].append(strike)

            # Small delay between requests
            ib.sleep(0.2)

        logger.info(f"Retrieved option chain with prices for {symbol} expiry {expiry}")
        return option_data

    except Exception as e:
        logger.error(f"Error fetching option chain with prices: {str(e)}")
        return None

def check_market_data_permissions(ib):
    """Check what market data permissions are available

    Args:
        ib: IB connection object

    Returns:
        Dictionary with permission status
    """
    logger = setup_logger()
    try:
        # Test with a simple stock request
        test_symbol = 'SPY'
        contract = Stock(test_symbol, 'SMART', 'USD')
        ib.qualifyContracts(contract)

        # Try real-time data first
        logger.info("Testing real-time market data permissions...")
        ticker = ib.reqMktData(contract, '', False, False)
        ib.sleep(2)

        has_realtime = False
        has_delayed = False

        # Check if we got real-time data
        if hasattr(ticker, 'last') and ticker.last > 0:
            has_realtime = True
            logger.info("Real-time market data is available")
        else:
            logger.info("Real-time market data not available")

        # Cancel and try delayed data
        ib.cancelMktData(contract)
        ib.sleep(0.5)

        logger.info("Testing delayed market data permissions...")
        ticker = ib.reqMktData(contract, '', True, False)  # snapshot for delayed
        ib.sleep(2)

        if hasattr(ticker, 'last') and ticker.last > 0:
            has_delayed = True
            logger.info("Delayed market data is available")
        else:
            logger.info("Delayed market data not available")

        ib.cancelMktData(contract)

        return {
            'real_time': has_realtime,
            'delayed': has_delayed,
            'test_symbol': test_symbol
        }

    except Exception as e:
        logger.error(f"Error checking market data permissions: {str(e)}")
        return {
            'real_time': False,
            'delayed': False,
            'error': str(e)
        }


