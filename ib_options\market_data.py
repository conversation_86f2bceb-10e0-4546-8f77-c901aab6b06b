from ib_insync import Stock, Option
from ib_options.connect import setup_logger
import math

def get_option_chain(ib, symbol, exchange='SMART'):
    """Fetch option chain for a given underlying symbol
    
    Args:
        ib: IB connection object
        symbol: Underlying symbol (e.g., 'SPY')
        exchange: Exchange (default: 'SMART')
        
    Returns:
        Option chain data or None if error occurs
    """
    logger = setup_logger()
    try:
        # Create contract for underlying
        contract = Stock(symbol, exchange, 'USD')
        
        # Request contract details to ensure we have the correct contract
        ib.qualifyContracts(contract)
        
        # Request option chain
        chains = ib.reqSecDefOptParams(contract.symbol, '', contract.secType, contract.conId)
        
        if not chains:
            logger.warning(f"No option chains found for {symbol}")
            return None
            
        logger.info(f"Retrieved option chain for {symbol}")
        return chains
    except Exception as e:
        logger.error(f"Error fetching option chain for {symbol}: {str(e)}")
        return None

def get_option_market_data(ib, symbol, expiry, strike, right, exchange='SMART'):
    """Get market data for a specific option contract
    
    Args:
        ib: IB connection object
        symbol: Underlying symbol (e.g., 'SPY')
        expiry: Expiration date in format YYYYMMDD
        strike: Strike price
        right: 'C' for Call, 'P' for Put
        exchange: Exchange (default: 'SMART')
        
    Returns:
        Dictionary with market data or None if error occurs
    """
    logger = setup_logger()
    try:
        # Create option contract
        contract = Option(symbol, expiry, strike, right, exchange)
        
        # Qualify the contract
        qualified_contracts = ib.qualifyContracts(contract)
        if not qualified_contracts:
            logger.error(f"Could not qualify option contract: {symbol} {expiry} {strike} {right}")
            return None
            
        contract = qualified_contracts[0]
        
        # Request market data with delayed data flag
        ticker = ib.reqMktData(contract, '', False, False)
        ib.sleep(1)  # Wait for data to arrive
        
        # Compile market data - handle missing attributes safely
        market_data = {
            'symbol': symbol,
            'expiry': expiry,
            'strike': strike,
            'right': right,
            'bid': getattr(ticker, 'bid', 0) if not math.isnan(getattr(ticker, 'bid', 0)) else 0,
            'ask': getattr(ticker, 'ask', 0) if not math.isnan(getattr(ticker, 'ask', 0)) else 0,
            'last': getattr(ticker, 'last', 0) if not math.isnan(getattr(ticker, 'last', 0)) else 0,
            'volume': getattr(ticker, 'volume', 0) if not math.isnan(getattr(ticker, 'volume', 0)) else 0,
            'open_interest': getattr(ticker, 'openInterest', 0) if not math.isnan(getattr(ticker, 'openInterest', 0)) else 0,
            'implied_volatility': getattr(ticker, 'impliedVolatility', 0) if not math.isnan(getattr(ticker, 'impliedVolatility', 0)) else 0
        }
        
        logger.info(f"Retrieved market data for {symbol} {right} {strike} {expiry}")
        return market_data
    except Exception as e:
        logger.error(f"Error fetching option market data: {str(e)}")
        return None



