import requests
from src.config import API_URL

def login_user(username, password):
    """Authenticate user and return session token."""
    url = f"{API_URL}/login"
    payload = {
        'username': username,
        'password': password
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        return response.json().get('token')
    else:
        raise Exception("Login failed: " + response.text)