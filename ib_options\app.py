"""
Main Application for IB Options Trading POC
"""

import sys
import signal
from typing import Optional, Dict, Any
from datetime import datetime
import json

from .client import IBOptionsClient
from .models import OptionContract, Order, OptionRight, OrderType, OrderAction
from .config import config
from .connect import setup_logger

class IBOptionsApp:
    """Main application class for IB Options Trading POC"""
    
    def __init__(self):
        self.logger = setup_logger('IBOptionsApp')
        self.client = IBOptionsClient()
        self.running = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.shutdown()
        sys.exit(0)
    
    def startup(self) -> bool:
        """Initialize and start the application"""
        try:
            self.logger.info("Starting IB Options Trading POC")
            self.logger.info(f"Configuration: {config.to_dict()}")
            
            # Validate configuration
            config.validate()
            config.ensure_directories()
            
            # Connect to IB
            if not self.client.connect():
                self.logger.error("Failed to connect to Interactive Brokers")
                return False
            
            self.running = True
            self.logger.info("Application started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting application: {e}")
            return False
    
    def shutdown(self):
        """Shutdown the application"""
        try:
            self.logger.info("Shutting down application")
            self.running = False
            
            if self.client:
                self.client.disconnect()
            
            self.logger.info("Application shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    def run_interactive(self):
        """Run interactive menu-driven interface"""
        if not self.startup():
            return 1
        
        try:
            while self.running:
                self._display_menu()
                choice = input("\nEnter your choice: ").strip()
                
                if choice == '0':
                    break
                
                self._handle_menu_choice(choice)
                
                if self.running:
                    input("\nPress Enter to continue...")
        
        except KeyboardInterrupt:
            self.logger.info("Interrupted by user")
        except Exception as e:
            self.logger.error(f"Error in interactive mode: {e}")
        finally:
            self.shutdown()
        
        return 0
    
    def _display_menu(self):
        """Display the main menu"""
        print("\n" + "="*60)
        print("===== IB Options Trading POC - Professional Edition =====")
        print("="*60)
        print("1. Get Account Balances")
        print("2. Get Open Option Positions")
        print("3. Get Option Chain for Symbol")
        print("4. Get Option Market Data")
        print("5. Place Option Order (Market)")
        print("6. Place Option Order (Limit)")
        print("7. Get Order Status")
        print("8. Cancel Order")
        print("9. Get Available Symbols")
        print("10. System Status & Cache Info")
        print("0. Exit")
        print("="*60)
    
    def _handle_menu_choice(self, choice: str):
        """Handle menu selection"""
        try:
            if choice == '1':
                self._handle_account_balances()
            elif choice == '2':
                self._handle_option_positions()
            elif choice == '3':
                self._handle_option_chain()
            elif choice == '4':
                self._handle_option_market_data()
            elif choice == '5':
                self._handle_market_order()
            elif choice == '6':
                self._handle_limit_order()
            elif choice == '7':
                self._handle_order_status()
            elif choice == '8':
                self._handle_cancel_order()
            elif choice == '9':
                self._handle_available_symbols()
            elif choice == '10':
                self._handle_system_status()
            else:
                print("Invalid choice. Please try again.")
                
        except Exception as e:
            self.logger.error(f"Error handling menu choice {choice}: {e}")
            print(f"Error: {e}")
    
    def _handle_account_balances(self):
        """Handle account balances request"""
        print("\n--- Account Balances ---")
        balances = self.client.get_account_balances()
        
        if balances:
            print(f"Available Funds: ${balances.available_funds:,.2f}")
            print(f"Buying Power: ${balances.buying_power:,.2f}")
            print(f"Net Liquidation: ${balances.net_liquidation:,.2f}")
            print(f"Option Market Value: ${balances.option_market_value:,.2f}")
            print(f"Unrealized P&L: ${balances.unrealized_pnl:,.2f}")
            print(f"Realized P&L: ${balances.realized_pnl:,.2f}")
            print(f"Maintenance Margin: ${balances.maintenance_margin:,.2f}")
            print(f"Initial Margin: ${balances.initial_margin:,.2f}")
            print(f"Excess Liquidity: ${balances.excess_liquidity:,.2f}")
        else:
            print("Failed to retrieve account balances")
    
    def _handle_option_positions(self):
        """Handle option positions request"""
        print("\n--- Open Option Positions ---")
        positions = self.client.get_option_positions()
        
        if positions:
            for i, pos in enumerate(positions, 1):
                contract = pos.contract
                print(f"\n{i}. {contract.symbol} {contract.right.value} ${contract.strike} {contract.expiry}")
                print(f"   Position: {pos.position} contracts")
                print(f"   Avg Cost: ${pos.avg_cost:.2f}")
                if pos.market_price > 0:
                    print(f"   Market Price: ${pos.market_price:.2f}")
                    print(f"   Market Value: ${pos.market_value:,.2f}")
                    print(f"   Unrealized P&L: ${pos.unrealized_pnl:,.2f} ({pos.pnl_percentage:.2f}%)")
        else:
            print("No open option positions found")
    
    def _handle_option_chain(self):
        """Handle option chain request"""
        print("\n--- Option Chain ---")
        symbol = input("Enter symbol (e.g., SPY): ").upper().strip()
        
        if not symbol:
            print("Invalid symbol")
            return
        
        chain = self.client.get_option_chain(symbol)
        
        if chain:
            print(f"\nOption Chain for {symbol}")
            print(f"Underlying Price: ${chain.underlying_price:.2f}")
            print(f"Exchange: {chain.exchange}")
            print(f"Expirations ({len(chain.expirations)}): {', '.join(chain.expirations[:5])}...")
            print(f"Strikes ({len(chain.strikes)}): {', '.join([str(s) for s in chain.strikes[:10]])}...")
        else:
            print(f"Failed to retrieve option chain for {symbol}")
    
    def _handle_option_market_data(self):
        """Handle option market data request"""
        print("\n--- Option Market Data ---")
        try:
            symbol = input("Enter symbol (e.g., SPY): ").upper().strip()
            right_input = input("Call or Put (C/P): ").upper().strip()
            
            right = None
            if right_input == 'C':
                right = OptionRight.CALL
            elif right_input == 'P':
                right = OptionRight.PUT
            else:
                print("Invalid option type. Must be C or P.")
                return
                
            strike_input = input("Strike price: ").strip()
            try:
                strike = float(strike_input)
            except ValueError:
                print("Invalid strike price. Must be a number.")
                return
                
            expiry = input("Expiration (YYYYMMDD): ").strip()
            
            # Validate expiry format
            if not expiry.isdigit() or len(expiry) != 8:
                print("Invalid expiration format. Must be YYYYMMDD (8 digits).")
                return
                
            # Create contract
            contract = OptionContract(
                symbol=symbol,
                expiry=expiry,
                strike=strike,
                right=right
            )
            
            # Get market data
            market_data = self.client.get_option_market_data(contract)
            
            if market_data:
                print(f"\nMarket Data for {symbol} {right.value} ${strike} {expiry}")
                print(f"Data Type: {market_data.data_type}")
                print(f"Bid: ${market_data.bid:.2f}")
                print(f"Ask: ${market_data.ask:.2f}")
                print(f"Mid: ${market_data.mid_price:.2f}")
                print(f"Last: ${market_data.last:.2f}")
                print(f"Volume: {market_data.volume:,}")
                print(f"Open Interest: {market_data.open_interest:,}")
                print(f"Implied Volatility: {market_data.implied_volatility:.4f}")
                
                if config.market_data.enable_greeks:
                    print(f"\nGreeks:")
                    print(f"Delta: {market_data.delta:.4f}")
                    print(f"Gamma: {market_data.gamma:.4f}")
                    print(f"Theta: {market_data.theta:.4f}")
                    print(f"Vega: {market_data.vega:.4f}")
            else:
                print("Failed to retrieve market data")
        except Exception as e:
            self.logger.error(f"Error handling market data request: {e}")
            print(f"Error: {e}")
    
    def _handle_market_order(self):
        """Handle market order placement"""
        print("\n--- Place Market Order ---")
        self._place_order(OrderType.MARKET)
    
    def _handle_limit_order(self):
        """Handle limit order placement"""
        print("\n--- Place Limit Order ---")
        self._place_order(OrderType.LIMIT)
    
    def _place_order(self, order_type: OrderType):
        """Place an order"""
        try:
            symbol = input("Enter symbol (e.g., SPY): ").upper().strip()
            right = input("Call or Put (C/P): ").upper().strip()
            strike = float(input("Strike price: "))
            expiry = input("Expiration (YYYYMMDD): ").strip()
            action = input("BUY or SELL: ").upper().strip()
            quantity = int(input("Number of contracts: "))
            
            limit_price = None
            if order_type == OrderType.LIMIT:
                limit_price = float(input("Limit price: "))
            
            contract = OptionContract(
                symbol=symbol,
                expiry=expiry,
                strike=strike,
                right=OptionRight(right)
            )
            
            order = Order(
                contract=contract,
                action=OrderAction(action),
                quantity=quantity,
                order_type=order_type,
                limit_price=limit_price
            )
            
            order_id = self.client.place_option_order(order)
            
            if order_id:
                print(f"Order placed successfully! Order ID: {order_id}")
                
                # Get immediate status
                import time
                time.sleep(1)
                status = self.client.get_order_status(order_id)
                if status:
                    print(f"Status: {status.status}")
                    print(f"Filled: {status.filled}")
                    print(f"Remaining: {status.remaining}")
            else:
                print("Failed to place order")
                
        except ValueError as e:
            print(f"Invalid input: {e}")
        except Exception as e:
            print(f"Error: {e}")
    
    def _handle_order_status(self):
        """Handle order status request"""
        print("\n--- Order Status ---")
        
        choice = input("Check specific order? (y/n): ").lower().strip()
        
        if choice == 'y':
            try:
                order_id = int(input("Enter order ID: "))
                status = self.client.get_order_status(order_id)
                
                if status:
                    print(f"\nOrder {order_id} Status:")
                    print(f"Status: {status.status}")
                    print(f"Filled: {status.filled}")
                    print(f"Remaining: {status.remaining}")
                    print(f"Avg Fill Price: ${status.avg_fill_price:.2f}")
                    print(f"Commission: ${status.commission:.2f}")
                else:
                    print(f"Order {order_id} not found")
            except ValueError:
                print("Invalid order ID")
        else:
            statuses = self.client.get_order_status()
            
            if statuses:
                print(f"\nAll Orders ({len(statuses)}):")
                for status in statuses:
                    print(f"Order {status.order_id}: {status.status} (Filled: {status.filled}, Remaining: {status.remaining})")
            else:
                print("No orders found")
    
    def _handle_cancel_order(self):
        """Handle order cancellation"""
        print("\n--- Cancel Order ---")
        
        try:
            order_id = int(input("Enter order ID to cancel: "))
            success = self.client.cancel_order(order_id)
            
            if success:
                print(f"Order {order_id} cancelled successfully")
            else:
                print(f"Failed to cancel order {order_id}")
        except ValueError:
            print("Invalid order ID")
    
    def _handle_available_symbols(self):
        """Handle available symbols request"""
        print("\n--- Available Symbols ---")
        symbols = self.client.get_available_symbols()
        
        print(f"Common option symbols ({len(symbols)}):")
        for i, symbol in enumerate(symbols, 1):
            print(f"{i:2d}. {symbol}")
    
    def _handle_system_status(self):
        """Handle system status request"""
        print("\n--- System Status ---")
        
        print(f"Connection Status: {'Connected' if self.client.is_connected() else 'Disconnected'}")
        print(f"Configuration: {config.trading.default_exchange} exchange, {config.trading.default_currency} currency")
        print(f"Market Data: {'Delayed' if config.market_data.use_delayed_data else 'Real-time'}")
        print(f"Greeks Enabled: {config.market_data.enable_greeks}")
        
        cache_stats = self.client.get_cache_stats()
        print(f"Cache Size: {cache_stats['cache_size']} items")
        
        if cache_stats['cache_size'] > 0:
            clear_cache = input("Clear cache? (y/n): ").lower().strip()
            if clear_cache == 'y':
                self.client.clear_cache()
                print("Cache cleared")

def main():
    """Main entry point"""
    app = IBOptionsApp()
    return app.run_interactive()

if __name__ == "__main__":
    sys.exit(main())

