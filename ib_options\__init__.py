"""
IB Options Trading POC - Professional Edition

A comprehensive Python POC for Options trading with Interactive Brokers,
developed for GoTrade integration.

Phase 1: Python POC Development
Authors: <AUTHORS>
Version: 1.0.0
"""

__version__ = "1.0.0"
__authors__ = ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]
__email__ = ["jugra<PERSON><PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>"]

# Core exports
from .client import IBOptionsClient
from .models import (
    OptionContract, MarketData, OptionChain, Order, OrderStatus,
    Position, AccountBalance, OptionRight, OrderType, OrderAction
)
from .config import config
from .connect import setup_logger, connection_manager
from .metrics import performance_monitor, track_latency

__all__ = [
    # Core client
    'IBOptionsClient',

    # Data models
    'OptionContract', 'MarketData', 'OptionChain', 'Order', 'OrderStatus',
    'Position', 'AccountBalance',

    # Enums
    'OptionRight', 'OrderType', 'OrderAction',

    # Configuration and utilities
    'config', 'setup_logger', 'connection_manager',

    # Performance monitoring
    'performance_monitor', 'track_latency'
]