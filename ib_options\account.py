from ib_options.connect import setup_logger
from ib_insync import Option

def get_account_balances(ib):
    """Fetch account balances relevant to options trading
    
    Args:
        ib: IB connection object
        
    Returns:
        Dictionary of account balances or None if error occurs
    """
    logger = setup_logger()
    try:
        account_values = ib.accountSummary()
        
        # Filter for relevant values
        relevant_tags = ['AvailableFunds', 'BuyingPower', 'NetLiquidation', 
                         'OptionMarketValue', 'UnrealizedPnL', 'MaintMarginReq',
                         'InitMarginReq', 'ExcessLiquidity']
        
        balances = {item.tag: float(item.value) 
                   for item in account_values 
                   if item.tag in relevant_tags}
        
        logger.info(f"Account balances retrieved: {balances}")
        return balances
    except Exception as e:
        logger.error(f"Error fetching account balances: {str(e)}")
        return None

def get_open_option_positions(ib):
    """Fetch current open options positions
    
    Args:
        ib: IB connection object
        
    Returns:
        List of dictionaries containing position details or None if error occurs
    """
    logger = setup_logger()
    try:
        positions = ib.positions()
        
        # Filter for options positions
        option_positions = [p for p in positions if p.contract.secType == 'OPT']
        
        # Format positions for easier reading
        formatted_positions = []
        for p in option_positions:
            contract = p.contract
            formatted_positions.append({
                'symbol': contract.symbol,
                'right': contract.right,  # 'C' for Call, 'P' for Put
                'strike': contract.strike,
                'expiry': contract.lastTradeDateOrContractMonth,
                'position': p.position,
                'avgCost': p.avgCost,
                'marketPrice': 0.0,  # Will be updated if market data is available
                'marketValue': 0.0,  # Will be updated if market data is available
                'pnl': 0.0           # Will be updated if market data is available
            })
        
        # Try to get market data for positions
        for pos in formatted_positions:
            try:
                contract = ib.qualifyContracts(
                    Option(pos['symbol'], pos['expiry'], pos['strike'], 
                           pos['right'], 'SMART')
                )[0]
                
                # Request market data
                ticker = ib.reqMktData(contract)
                ib.sleep(1)  # Wait for data to arrive
                
                if ticker.marketPrice() > 0:
                    pos['marketPrice'] = ticker.marketPrice()
                    pos['marketValue'] = pos['marketPrice'] * abs(pos['position']) * 100  # 100 multiplier for options
                    pos['pnl'] = (pos['marketPrice'] - pos['avgCost']) * pos['position'] * 100
            except Exception as e:
                logger.warning(f"Could not get market data for {pos['symbol']} {pos['right']} {pos['strike']} {pos['expiry']}: {str(e)}")
        
        logger.info(f"Retrieved {len(formatted_positions)} open option positions")
        return formatted_positions
    except Exception as e:
        logger.error(f"Error fetching option positions: {str(e)}")
        return None
