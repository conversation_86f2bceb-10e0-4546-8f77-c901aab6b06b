"""
Data models for IB Options Trading POC
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, List, Any, Union
from datetime import datetime, date
from enum import Enum
import json

class OptionRight(Enum):
    """Option right enumeration"""
    CALL = "C"
    PUT = "P"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "MKT"
    LIMIT = "LMT"
    STOP = "STP"
    STOP_LIMIT = "STP LMT"

class OrderAction(Enum):
    """Order action enumeration"""
    BUY = "BUY"
    SELL = "SELL"

class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING_SUBMIT = "PendingSubmit"
    PENDING_CANCEL = "PendingCancel"
    PRE_SUBMITTED = "PreSubmitted"
    SUBMITTED = "Submitted"
    CANCELLED = "Cancelled"
    FILLED = "Filled"
    INACTIVE = "Inactive"
    PARTIAL_FILLED = "PartiallyFilled"
    API_CANCELLED = "ApiCancelled"
    API_PENDING = "ApiPending"

@dataclass
class OptionContract:
    """Option contract data model"""
    symbol: str
    expiry: str  # YYYYMMDD format
    strike: float
    right: OptionRight
    exchange: str = "SMART"
    currency: str = "USD"
    multiplier: int = 100
    trading_class: Optional[str] = None
    contract_id: Optional[int] = None
    local_symbol: Optional[str] = None
    
    def __post_init__(self):
        if isinstance(self.right, str):
            self.right = OptionRight(self.right)
        if self.trading_class is None:
            self.trading_class = self.symbol
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "symbol": self.symbol,
            "expiry": self.expiry,
            "strike": self.strike,
            "right": self.right.value,
            "exchange": self.exchange,
            "currency": self.currency,
            "multiplier": self.multiplier,
            "trading_class": self.trading_class,
            "contract_id": self.contract_id,
            "local_symbol": self.local_symbol
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OptionContract':
        """Create from dictionary"""
        return cls(**data)

@dataclass
class MarketData:
    """Market data for an option contract"""
    contract: OptionContract
    bid: float = 0.0
    ask: float = 0.0
    last: float = 0.0
    close: float = 0.0
    volume: int = 0
    open_interest: int = 0
    implied_volatility: float = 0.0
    delta: float = 0.0
    gamma: float = 0.0
    theta: float = 0.0
    vega: float = 0.0
    rho: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    data_type: str = "delayed"  # "delayed" or "real-time"
    
    @property
    def mid_price(self) -> float:
        """Calculate mid price"""
        if self.bid > 0 and self.ask > 0:
            return (self.bid + self.ask) / 2
        return 0.0
    
    @property
    def spread(self) -> float:
        """Calculate bid-ask spread"""
        if self.bid > 0 and self.ask > 0:
            return self.ask - self.bid
        return 0.0
    
    @property
    def spread_percentage(self) -> float:
        """Calculate bid-ask spread as percentage of mid price"""
        mid = self.mid_price
        if mid > 0:
            return (self.spread / mid) * 100
        return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "contract": self.contract.to_dict(),
            "bid": self.bid,
            "ask": self.ask,
            "last": self.last,
            "close": self.close,
            "volume": self.volume,
            "open_interest": self.open_interest,
            "implied_volatility": self.implied_volatility,
            "delta": self.delta,
            "gamma": self.gamma,
            "theta": self.theta,
            "vega": self.vega,
            "rho": self.rho,
            "timestamp": self.timestamp.isoformat(),
            "data_type": self.data_type,
            "mid_price": self.mid_price,
            "spread": self.spread,
            "spread_percentage": self.spread_percentage
        }

@dataclass
class OptionChain:
    """Option chain data model"""
    symbol: str
    underlying_price: float
    expirations: List[str]
    strikes: List[float]
    exchange: str = "SMART"
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "symbol": self.symbol,
            "underlying_price": self.underlying_price,
            "expirations": self.expirations,
            "strikes": self.strikes,
            "exchange": self.exchange,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class Order:
    """Order data model"""
    contract: OptionContract
    action: OrderAction
    quantity: int
    order_type: OrderType
    limit_price: Optional[float] = None
    stop_price: Optional[float] = None
    order_id: Optional[int] = None
    parent_id: Optional[int] = None
    time_in_force: str = "DAY"
    good_after_time: Optional[str] = None
    good_till_date: Optional[str] = None
    outside_rth: bool = False
    hidden: bool = False
    discretionary_amount: float = 0.0
    
    def __post_init__(self):
        if isinstance(self.action, str):
            self.action = OrderAction(self.action)
        if isinstance(self.order_type, str):
            self.order_type = OrderType(self.order_type)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "contract": self.contract.to_dict(),
            "action": self.action.value,
            "quantity": self.quantity,
            "order_type": self.order_type.value,
            "limit_price": self.limit_price,
            "stop_price": self.stop_price,
            "order_id": self.order_id,
            "parent_id": self.parent_id,
            "time_in_force": self.time_in_force,
            "good_after_time": self.good_after_time,
            "good_till_date": self.good_till_date,
            "outside_rth": self.outside_rth,
            "hidden": self.hidden,
            "discretionary_amount": self.discretionary_amount
        }

@dataclass
class OrderStatus:
    """Order status data model"""
    order_id: int
    status: OrderStatus
    filled: int = 0
    remaining: int = 0
    avg_fill_price: float = 0.0
    last_fill_price: float = 0.0
    commission: float = 0.0
    min_commission: float = 0.0
    max_commission: float = 0.0
    commission_currency: str = "USD"
    warning_text: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        if isinstance(self.status, str):
            self.status = OrderStatus(self.status)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "order_id": self.order_id,
            "status": self.status.value,
            "filled": self.filled,
            "remaining": self.remaining,
            "avg_fill_price": self.avg_fill_price,
            "last_fill_price": self.last_fill_price,
            "commission": self.commission,
            "min_commission": self.min_commission,
            "max_commission": self.max_commission,
            "commission_currency": self.commission_currency,
            "warning_text": self.warning_text,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class Position:
    """Position data model"""
    contract: OptionContract
    position: int
    avg_cost: float
    market_price: float = 0.0
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def total_value(self) -> float:
        """Calculate total position value"""
        return self.market_price * abs(self.position) * self.contract.multiplier
    
    @property
    def pnl_percentage(self) -> float:
        """Calculate P&L as percentage"""
        if self.avg_cost > 0:
            return ((self.market_price - self.avg_cost) / self.avg_cost) * 100
        return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "contract": self.contract.to_dict(),
            "position": self.position,
            "avg_cost": self.avg_cost,
            "market_price": self.market_price,
            "market_value": self.market_value,
            "unrealized_pnl": self.unrealized_pnl,
            "realized_pnl": self.realized_pnl,
            "total_value": self.total_value,
            "pnl_percentage": self.pnl_percentage,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class AccountBalance:
    """Account balance data model"""
    available_funds: float = 0.0
    buying_power: float = 0.0
    net_liquidation: float = 0.0
    option_market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    maintenance_margin: float = 0.0
    initial_margin: float = 0.0
    excess_liquidity: float = 0.0
    currency: str = "USD"
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "available_funds": self.available_funds,
            "buying_power": self.buying_power,
            "net_liquidation": self.net_liquidation,
            "option_market_value": self.option_market_value,
            "unrealized_pnl": self.unrealized_pnl,
            "realized_pnl": self.realized_pnl,
            "maintenance_margin": self.maintenance_margin,
            "initial_margin": self.initial_margin,
            "excess_liquidity": self.excess_liquidity,
            "currency": self.currency,
            "timestamp": self.timestamp.isoformat()
        }
