"""
Performance metrics and monitoring for IB Options Trading POC
"""

import time
import threading
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque

from .connect import setup_logger
from .config import config

@dataclass
class PerformanceMetric:
    """Individual performance metric"""
    name: str
    value: float
    unit: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "value": self.value,
            "unit": self.unit,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class LatencyMetric:
    """Latency measurement"""
    operation: str
    duration_ms: float
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = True
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "operation": self.operation,
            "duration_ms": self.duration_ms,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "error_message": self.error_message
        }

class PerformanceMonitor:
    """Performance monitoring and metrics collection"""
    
    def __init__(self):
        self.logger = setup_logger('PerformanceMonitor')
        self.enabled = config.performance.enable_metrics
        
        # Metrics storage
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._latency_metrics: deque = deque(maxlen=10000)
        self._counters: Dict[str, int] = defaultdict(int)
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Background monitoring
        self._monitoring_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()
        
        if self.enabled:
            self.start_monitoring()
    
    def start_monitoring(self):
        """Start background monitoring"""
        if self._monitoring_thread is None or not self._monitoring_thread.is_alive():
            self._stop_monitoring.clear()
            self._monitoring_thread = threading.Thread(target=self._monitor_system, daemon=True)
            self._monitoring_thread.start()
            self.logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._stop_monitoring.set()
            self._monitoring_thread.join(timeout=5)
            self.logger.info("Performance monitoring stopped")
    
    def _monitor_system(self):
        """Background system monitoring"""
        while not self._stop_monitoring.wait(config.performance.metrics_interval):
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                self.record_metric("cpu_usage", cpu_percent, "percent")
                
                # Memory usage
                memory = psutil.virtual_memory()
                self.record_metric("memory_usage", memory.percent, "percent")
                self.record_metric("memory_used_mb", memory.used / 1024 / 1024, "MB")
                
                # Check memory limit
                if memory.used / 1024 / 1024 > config.performance.max_memory_usage_mb:
                    self.logger.warning(f"Memory usage ({memory.used / 1024 / 1024:.1f} MB) exceeds limit ({config.performance.max_memory_usage_mb} MB)")
                
                # Network I/O
                net_io = psutil.net_io_counters()
                self.record_metric("network_bytes_sent", net_io.bytes_sent, "bytes")
                self.record_metric("network_bytes_recv", net_io.bytes_recv, "bytes")
                
            except Exception as e:
                self.logger.error(f"Error in system monitoring: {e}")
    
    def record_metric(self, name: str, value: float, unit: str):
        """Record a performance metric"""
        if not self.enabled:
            return
        
        metric = PerformanceMetric(name, value, unit)
        
        with self._lock:
            self._metrics[name].append(metric)
    
    def record_latency(self, operation: str, duration_ms: float, success: bool = True, error_message: Optional[str] = None):
        """Record a latency measurement"""
        if not self.enabled:
            return
        
        latency = LatencyMetric(operation, duration_ms, success=success, error_message=error_message)
        
        with self._lock:
            self._latency_metrics.append(latency)
    
    def increment_counter(self, name: str, value: int = 1):
        """Increment a counter"""
        if not self.enabled:
            return
        
        with self._lock:
            self._counters[name] += value
    
    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Get metrics summary for the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            summary = {
                "period_hours": hours,
                "timestamp": datetime.now().isoformat(),
                "metrics": {},
                "latency": {},
                "counters": dict(self._counters)
            }
            
            # Process regular metrics
            for name, metrics in self._metrics.items():
                recent_metrics = [m for m in metrics if m.timestamp >= cutoff_time]
                if recent_metrics:
                    values = [m.value for m in recent_metrics]
                    summary["metrics"][name] = {
                        "count": len(values),
                        "min": min(values),
                        "max": max(values),
                        "avg": sum(values) / len(values),
                        "unit": recent_metrics[0].unit,
                        "latest": values[-1]
                    }
            
            # Process latency metrics
            recent_latencies = [l for l in self._latency_metrics if l.timestamp >= cutoff_time]
            latency_by_operation = defaultdict(list)
            
            for latency in recent_latencies:
                latency_by_operation[latency.operation].append(latency)
            
            for operation, latencies in latency_by_operation.items():
                durations = [l.duration_ms for l in latencies]
                success_count = sum(1 for l in latencies if l.success)
                
                summary["latency"][operation] = {
                    "count": len(durations),
                    "success_rate": success_count / len(durations) if durations else 0,
                    "min_ms": min(durations) if durations else 0,
                    "max_ms": max(durations) if durations else 0,
                    "avg_ms": sum(durations) / len(durations) if durations else 0,
                    "p95_ms": self._percentile(durations, 95) if durations else 0,
                    "p99_ms": self._percentile(durations, 99) if durations else 0
                }
        
        return summary
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile"""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int((percentile / 100) * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]
    
    def export_metrics(self, filename: Optional[str] = None) -> str:
        """Export metrics to JSON file"""
        if filename is None:
            filename = f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        summary = self.get_metrics_summary(24)  # Last 24 hours
        
        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)
        
        self.logger.info(f"Metrics exported to {filename}")
        return filename
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current system status"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            return {
                "monitoring_enabled": self.enabled,
                "monitoring_active": self._monitoring_thread and self._monitoring_thread.is_alive(),
                "cpu_usage_percent": cpu_percent,
                "memory_usage_percent": memory.percent,
                "memory_used_mb": memory.used / 1024 / 1024,
                "memory_available_mb": memory.available / 1024 / 1024,
                "metrics_count": sum(len(metrics) for metrics in self._metrics.values()),
                "latency_measurements": len(self._latency_metrics),
                "counters": dict(self._counters)
            }
        except Exception as e:
            self.logger.error(f"Error getting current status: {e}")
            return {"error": str(e)}

class LatencyTracker:
    """Context manager for tracking operation latency"""
    
    def __init__(self, monitor: PerformanceMonitor, operation: str):
        self.monitor = monitor
        self.operation = operation
        self.start_time = None
        self.success = True
        self.error_message = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration_ms = (time.time() - self.start_time) * 1000
            
            if exc_type is not None:
                self.success = False
                self.error_message = str(exc_val) if exc_val else str(exc_type)
            
            self.monitor.record_latency(
                self.operation, 
                duration_ms, 
                self.success, 
                self.error_message
            )
    
    def mark_error(self, error_message: str):
        """Mark the operation as failed"""
        self.success = False
        self.error_message = error_message

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

def track_latency(operation: str):
    """Decorator for tracking function latency"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with LatencyTracker(performance_monitor, operation):
                return func(*args, **kwargs)
        return wrapper
    return decorator

def get_performance_summary() -> Dict[str, Any]:
    """Get performance summary"""
    return performance_monitor.get_metrics_summary()

def export_performance_metrics(filename: Optional[str] = None) -> str:
    """Export performance metrics"""
    return performance_monitor.export_metrics(filename)
