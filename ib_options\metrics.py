"""
Performance metrics and monitoring for IB Options Trading POC
"""

import time
import threading
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import os
import gc
from contextlib import contextmanager

from .connect import setup_logger
from .config import config

@dataclass
class PerformanceMetric:
    """Individual performance metric"""
    name: str
    value: float
    unit: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "value": self.value,
            "unit": self.unit,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class LatencyMetric:
    """Latency measurement"""
    operation: str
    duration_ms: float
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = True
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "operation": self.operation,
            "duration_ms": self.duration_ms,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "error_message": self.error_message
        }

class PerformanceMonitor:
    """Performance monitoring for IB Options client"""
    
    def __init__(self):
        self.logger = setup_logger("IBOptions.PerformanceMonitor")
        self.latency_metrics = {}
        self.operation_counts = {}
        self.start_time = time.time()
        self.last_check = time.time()
        self.memory_usage = []
        
        # Start monitoring thread if enabled
        if config.performance.enable_monitoring:
            self.monitoring_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitoring_thread.start()
    
    def _monitor_loop(self):
        """Background monitoring loop"""
        while True:
            try:
                # Check system resources
                self._check_system_resources()
                
                # Sleep for monitoring interval
                time.sleep(config.performance.monitoring_interval)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
    
    def _check_system_resources(self):
        """Check system resources and log if thresholds exceeded"""
        try:
            # Get current memory usage
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.memory_usage.append(memory_mb)
            
            # Trim memory usage history
            if len(self.memory_usage) > 100:
                self.memory_usage = self.memory_usage[-100:]
            
            # Check if memory usage exceeds threshold
            if memory_mb > config.performance.memory_threshold:
                self.logger.warning(f"Memory usage ({memory_mb:.1f} MB) exceeds limit ({config.performance.memory_threshold} MB)")
                
                # Attempt to clean up memory if it's critically high
                if memory_mb > config.performance.memory_threshold * 2:
                    self.logger.warning("Memory usage critically high, attempting cleanup")
                    gc.collect()  # Force garbage collection
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if cpu_percent > config.performance.cpu_threshold:
                self.logger.warning(f"CPU usage ({cpu_percent}%) exceeds threshold ({config.performance.cpu_threshold}%)")
                
        except Exception as e:
            self.logger.error(f"Error checking system resources: {e}")
    
    @contextmanager
    def track_latency(self, operation_name):
        """Context manager to track operation latency"""
        start_time = time.time()
        try:
            yield
        finally:
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            # Update latency metrics
            if operation_name not in self.latency_metrics:
                self.latency_metrics[operation_name] = {
                    'count': 0,
                    'total_ms': 0,
                    'min_ms': float('inf'),
                    'max_ms': 0
                }
            
            metrics = self.latency_metrics[operation_name]
            metrics['count'] += 1
            metrics['total_ms'] += latency_ms
            metrics['min_ms'] = min(metrics['min_ms'], latency_ms)
            metrics['max_ms'] = max(metrics['max_ms'], latency_ms)
            
            # Log slow operations
            if latency_ms > config.performance.slow_operation_threshold:
                self.logger.warning(f"Slow operation: {operation_name} took {latency_ms:.2f}ms")
    
    def increment_counter(self, counter_name):
        """Increment an operation counter"""
        if counter_name not in self.operation_counts:
            self.operation_counts[counter_name] = 0
        self.operation_counts[counter_name] += 1
    
    def get_metrics(self):
        """Get current performance metrics"""
        # Calculate average latencies
        avg_latencies = {}
        for op, metrics in self.latency_metrics.items():
            if metrics['count'] > 0:
                avg_latencies[op] = metrics['total_ms'] / metrics['count']
        
        # Calculate memory stats
        memory_stats = {
            'current_mb': self.memory_usage[-1] if self.memory_usage else 0,
            'avg_mb': sum(self.memory_usage) / len(self.memory_usage) if self.memory_usage else 0,
            'max_mb': max(self.memory_usage) if self.memory_usage else 0
        }
        
        # Calculate uptime
        uptime_seconds = time.time() - self.start_time
        
        return {
            'latency': self.latency_metrics,
            'avg_latency': avg_latencies,
            'operations': self.operation_counts,
            'memory': memory_stats,
            'uptime_seconds': uptime_seconds
        }
    
    def reset_metrics(self):
        """Reset all metrics"""
        self.latency_metrics = {}
        self.operation_counts = {}
        self.start_time = time.time()
        self.memory_usage = []
        self.logger.info("Performance metrics reset")

class LatencyTracker:
    """Context manager for tracking operation latency"""
    
    def __init__(self, monitor: PerformanceMonitor, operation: str):
        self.monitor = monitor
        self.operation = operation
        self.start_time = None
        self.success = True
        self.error_message = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration_ms = (time.time() - self.start_time) * 1000
            
            if exc_type is not None:
                self.success = False
                self.error_message = str(exc_val) if exc_val else str(exc_type)
            
            self.monitor.record_latency(
                self.operation, 
                duration_ms, 
                self.success, 
                self.error_message
            )
    
    def mark_error(self, error_message: str):
        """Mark the operation as failed"""
        self.success = False
        self.error_message = error_message

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

def track_latency(operation: str):
    """Decorator for tracking function latency"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with LatencyTracker(performance_monitor, operation):
                return func(*args, **kwargs)
        return wrapper
    return decorator

def get_performance_summary() -> Dict[str, Any]:
    """Get performance summary"""
    return performance_monitor.get_metrics_summary()

def export_performance_metrics(filename: Optional[str] = None) -> str:
    """Export performance metrics"""
    return performance_monitor.export_metrics(filename)

