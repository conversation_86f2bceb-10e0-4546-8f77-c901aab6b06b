"""
Performance metrics and monitoring for IB Options Trading POC
"""

import time
import threading
import json
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

from .connect import setup_logger
from .config import config

@dataclass
class PerformanceMetric:
    """Individual performance metric"""
    name: str
    value: float
    unit: str
    timestamp: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "value": self.value,
            "unit": self.unit,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class LatencyMetric:
    """Latency measurement"""
    operation: str
    duration_ms: float
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = True
    error_message: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "operation": self.operation,
            "duration_ms": self.duration_ms,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "error_message": self.error_message
        }

class PerformanceMonitor:
    """Simplified performance monitoring for IB Options Trading POC"""

    def __init__(self):
        self.logger = setup_logger('PerformanceMonitor')
        self.enabled = config.performance.enable_metrics

        # Simple metrics storage
        self.metrics = {}
        self.latency_metrics = {}
        self.counters = {}
        self.start_time = time.time()

        # Thread safety
        self._lock = threading.Lock()

        if self.enabled:
            self.logger.info("Performance monitoring enabled")

    def record_metric(self, name: str, value: float, unit: str = ""):
        """Record a performance metric"""
        if not self.enabled:
            return

        with self._lock:
            self.metrics[name] = PerformanceMetric(name, value, unit)

    def record_latency(self, operation: str, duration_ms: float, success: bool = True, error_message: Optional[str] = None):
        """Record a latency measurement"""
        if not self.enabled:
            return

        with self._lock:
            if operation not in self.latency_metrics:
                self.latency_metrics[operation] = []

            self.latency_metrics[operation].append(
                LatencyMetric(operation, duration_ms, success=success, error_message=error_message)
            )

            # Keep only last 100 measurements per operation
            if len(self.latency_metrics[operation]) > 100:
                self.latency_metrics[operation] = self.latency_metrics[operation][-100:]

    def increment_counter(self, name: str, value: int = 1):
        """Increment a counter"""
        if not self.enabled:
            return

        with self._lock:
            if name not in self.counters:
                self.counters[name] = 0
            self.counters[name] += value

    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Get metrics summary"""
        if not self.enabled:
            return {"enabled": False}

        with self._lock:
            summary = {
                "enabled": True,
                "uptime_seconds": time.time() - self.start_time,
                "metrics": {name: metric.to_dict() for name, metric in self.metrics.items()},
                "counters": dict(self.counters),
                "latency": {}
            }

            # Process latency metrics
            for operation, measurements in self.latency_metrics.items():
                if measurements:
                    durations = [m.duration_ms for m in measurements]
                    success_count = sum(1 for m in measurements if m.success)

                    summary["latency"][operation] = {
                        "count": len(measurements),
                        "success_rate": success_count / len(measurements),
                        "avg_ms": sum(durations) / len(durations),
                        "min_ms": min(durations),
                        "max_ms": max(durations)
                    }

            return summary

    def export_metrics(self, filename: Optional[str] = None) -> str:
        """Export metrics to JSON file"""
        if filename is None:
            filename = f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        summary = self.get_metrics_summary()

        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)

        self.logger.info(f"Metrics exported to {filename}")
        return filename

    def start_monitoring(self):
        """Start monitoring (placeholder for compatibility)"""
        pass

    def stop_monitoring(self):
        """Stop monitoring (placeholder for compatibility)"""
        pass

class LatencyTracker:
    """Context manager for tracking operation latency"""

    def __init__(self, monitor: PerformanceMonitor, operation: str):
        self.monitor = monitor
        self.operation = operation
        self.start_time = None
        self.success = True
        self.error_message = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration_ms = (time.time() - self.start_time) * 1000

            if exc_type is not None:
                self.success = False
                self.error_message = str(exc_val) if exc_val else str(exc_type)

            self.monitor.record_latency(
                self.operation,
                duration_ms,
                self.success,
                self.error_message
            )

    def mark_error(self, error_message: str):
        """Mark the operation as failed"""
        self.success = False
        self.error_message = error_message

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

def track_latency(operation: str):
    """Decorator for tracking function latency"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with LatencyTracker(performance_monitor, operation):
                return func(*args, **kwargs)
        return wrapper
    return decorator

def get_performance_summary() -> Dict[str, Any]:
    """Get performance summary"""
    return performance_monitor.get_metrics_summary()

def export_performance_metrics(filename: Optional[str] = None) -> str:
    """Export performance metrics"""
    return performance_monitor.export_metrics(filename)