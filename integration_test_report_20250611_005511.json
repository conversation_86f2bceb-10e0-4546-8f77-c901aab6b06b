{"test_suite": "IB Options Trading POC Integration Test", "phase": "Phase 1: Python POC Development", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "timestamp": "2025-06-11T00:55:11.310544", "duration_seconds": 19.096028, "tests_passed": 6, "tests_total": 10, "success_rate": 60.0, "results": {"API Connection & Authentication": {"status": "PASS", "timestamp": "2025-06-11T00:54:52.379515"}, "Account Balances": {"status": "FAIL", "timestamp": "2025-06-11T00:54:52.379515"}, "Open Options Positions": {"status": "FAIL", "timestamp": "2025-06-11T00:54:52.380515"}, "Option Chains & Available Symbols": {"status": "FAIL", "timestamp": "2025-06-11T00:54:54.399578"}, "Option Market Data": {"status": "PASS", "timestamp": "2025-06-11T00:55:00.565458"}, "Market Order Placement": {"status": "PASS", "timestamp": "2025-06-11T00:55:05.517614"}, "Limit Order Placement": {"status": "PASS", "timestamp": "2025-06-11T00:55:10.546539"}, "Order Status Tracking": {"status": "FAIL", "timestamp": "2025-06-11T00:55:10.547543"}, "Error Handling & Logging": {"status": "PASS", "timestamp": "2025-06-11T00:55:11.308543"}, "Performance & Monitoring": {"status": "PASS", "timestamp": "2025-06-11T00:55:11.310544"}}, "configuration": {"ib_connection": {"host": "127.0.0.1", "port": 7497, "client_id": 1, "timeout": 30, "max_retries": 3, "retry_delay": 1.0}, "trading": {"default_exchange": "SMART", "default_currency": "USD", "max_order_quantity": 1000, "default_order_timeout": 300, "enable_paper_trading": true, "risk_check_enabled": true}, "market_data": {"use_delayed_data": true, "market_data_timeout": 10, "max_concurrent_requests": 50, "cache_duration": 60, "enable_greeks": true}, "logging": {"log_level": "INFO", "log_dir": "logs", "log_file_prefix": "ib_options", "max_log_files": 30, "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "enable_console_logging": true, "enable_file_logging": true}, "performance": {"enable_metrics": true, "metrics_interval": 60, "enable_benchmarking": false, "max_memory_usage_mb": 512, "enable_profiling": false}}}