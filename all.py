from ib_insync import *
import logging
import sys
from datetime import datetime
import pandas as pd

def setup_logger():
    logger = logging.getLogger('IBConnection')
    logger.setLevel(logging.DEBUG)
    
    fh = logging.FileHandler(f'ib_logs_{datetime.now().strftime("%Y%m%d")}.log')
    fh.setLevel(logging.DEBUG)
    
    ch = logging.StreamHandler(sys.stdout)
    ch.setLevel(logging.INFO)
    
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)
    
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    return logger

def connect_ib(port=7497, client_id=1):
    """Connect to Interactive Brokers TWS or Gateway
    
    Args:
        port: Port number (7497 for paper trading, 7496 for live)
        client_id: Unique client ID
    
    Returns:
        IB connection object or None if connection fails
    """
    logger = setup_logger()
    
    try:
        ib = IB()
        
        logger.info(f"Attempting to connect to Interactive Brokers on port {port}...")
        ib.connect('127.0.0.1', port, clientId=client_id)
        
        if ib.isConnected():
            logger.info("Successfully connected to Interactive Brokers")
            return ib
        else:
            logger.error("Failed to connect to Interactive Brokers")
            return None
            
    except Exception as e:
        logger.error(f"Error connecting to Interactive Brokers: {str(e)}")
        return None

def get_account_balances(ib):
    """Fetch account balances relevant to options trading"""
    logger = setup_logger()
    try:
        account_values = ib.accountSummary()
        
        # Filter for relevant values
        relevant_tags = ['AvailableFunds', 'BuyingPower', 'NetLiquidation', 
                         'OptionMarketValue', 'UnrealizedPnL']
        
        balances = {item.tag: float(item.value) 
                   for item in account_values 
                   if item.tag in relevant_tags}
        
        logger.info(f"Account balances retrieved: {balances}")
        return balances
    except Exception as e:
        logger.error(f"Error fetching account balances: {str(e)}")
        return None

def get_open_option_positions(ib):
    """Fetch current open options positions"""
    logger = setup_logger()
    try:
        positions = ib.positions()
        
        # Filter for options positions
        option_positions = [p for p in positions if p.contract.secType == 'OPT']
        
        # Format positions for easier reading
        formatted_positions = []
        for p in option_positions:
            contract = p.contract
            formatted_positions.append({
                'symbol': contract.symbol,
                'right': contract.right,  # 'C' for Call, 'P' for Put
                'strike': contract.strike,
                'expiry': contract.lastTradeDateOrContractMonth,
                'position': p.position,
                'avgCost': p.avgCost
            })
        
        logger.info(f"Retrieved {len(formatted_positions)} open option positions")
        return formatted_positions
    except Exception as e:
        logger.error(f"Error fetching option positions: {str(e)}")
        return None

def get_option_chain(ib, symbol, exchange='SMART'):
    """Fetch option chain for a given underlying symbol"""
    logger = setup_logger()
    try:
        # Create contract for underlying
        contract = Stock(symbol, exchange, 'USD')
        
        # Request contract details to ensure we have the correct contract
        ib.qualifyContracts(contract)
        
        # Request option chain
        chains = ib.reqSecDefOptParams(contract.symbol, '', contract.secType, contract.conId)
        
        if not chains:
            logger.warning(f"No option chains found for {symbol}")
            return None
            
        logger.info(f"Retrieved option chain for {symbol}")
        return chains
    except Exception as e:
        logger.error(f"Error fetching option chain for {symbol}: {str(e)}")
        return None

def create_option_contract(symbol, expiry, strike, right, exchange='SMART'):
    """Create an option contract
    
    Args:
        symbol: Underlying symbol (e.g., 'SPY')
        expiry: Expiration date in format YYYYMMDD
        strike: Strike price
        right: 'C' for Call, 'P' for Put
        exchange: Exchange (default 'SMART')
    
    Returns:
        Option contract object
    """
    return Option(symbol, expiry, strike, right, exchange, tradingClass=symbol)

def place_option_order(ib, contract, action, quantity, order_type='MKT', limit_price=None):
    """Place an option order
    
    Args:
        ib: IB connection object
        contract: Option contract
        action: 'BUY' or 'SELL'
        quantity: Number of contracts
        order_type: 'MKT' for market, 'LMT' for limit
        limit_price: Price for limit orders
    
    Returns:
        Trade object
    """
    logger = setup_logger()
    
    try:
        if order_type == 'MKT':
            order = MarketOrder(action, quantity)
        elif order_type == 'LMT':
            if limit_price is None:
                raise ValueError("Limit price must be provided for limit orders")
            order = LimitOrder(action, quantity, limit_price)
        else:
            raise ValueError(f"Unsupported order type: {order_type}")
        
        # Submit the order
        trade = ib.placeOrder(contract, order)
        
        logger.info(f"Placed {order_type} order: {action} {quantity} {contract.symbol} {contract.right} {contract.strike} {contract.lastTradeDateOrContractMonth}")
        return trade
    except Exception as e:
        logger.error(f"Error placing option order: {str(e)}")
        return None

def get_order_status(ib, trade=None):
    """Get status of orders
    
    Args:
        ib: IB connection object
        trade: Specific trade to check (if None, get all open orders)
    
    Returns:
        List of orders with their status
    """
    logger = setup_logger()
    
    try:
        if trade:
            # Update specific trade
            ib.sleep(0.1)  # Small delay to ensure order status is updated
            status = {
                'orderId': trade.order.orderId,
                'status': trade.orderStatus.status,
                'filled': trade.orderStatus.filled,
                'remaining': trade.orderStatus.remaining,
                'avgFillPrice': trade.orderStatus.avgFillPrice,
                'lastFillPrice': trade.orderStatus.lastFillPrice
            }
            return status
        else:
            # Get all open orders
            open_trades = ib.openTrades()
            statuses = []
            
            for trade in open_trades:
                statuses.append({
                    'orderId': trade.order.orderId,
                    'status': trade.orderStatus.status,
                    'filled': trade.orderStatus.filled,
                    'remaining': trade.orderStatus.remaining,
                    'avgFillPrice': trade.orderStatus.avgFillPrice,
                    'lastFillPrice': trade.orderStatus.lastFillPrice
                })
            
            logger.info(f"Retrieved status for {len(statuses)} orders")
            return statuses
    except Exception as e:
        logger.error(f"Error getting order status: {str(e)}")
        return None

if __name__ == "__main__":
    # Connect to IB TWS/Gateway (paper trading)
    ib = connect_ib(port=7497, client_id=1)
    
    if ib:
        try:
            # Example usage
            print("Account Balances:")
            balances = get_account_balances(ib)
            print(balances)
            
            print("\nOpen Option Positions:")
            positions = get_open_option_positions(ib)
            print(positions)
            
            # Example: Get SPY option chain
            print("\nFetching SPY option chain...")
            chains = get_option_chain(ib, 'SPY')
            if chains:
                chain = next(iter(chains))
                print(f"Expirations: {chain.expirations[:5]}...")
                print(f"Strikes: {chain.strikes[:5]}...")
            
            # Keep the connection alive
            ib.run()
        except KeyboardInterrupt:
            print("Interrupted by user")
        finally:
            ib.disconnect()
        print("Failed to connect to Interactive Brokers. Please check if TWS/Gateway is running.")
