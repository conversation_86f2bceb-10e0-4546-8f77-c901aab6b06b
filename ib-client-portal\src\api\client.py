import requests
from src.auth.login import login_user
from src.config import API_URL

def get_client_data(session_token):
    """Fetch client data from the API using the provided session token."""
    response = requests.get(f"{API_URL}/client/data", headers={"Authorization": f"Bearer {session_token}"})
    
    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()

def login_and_get_client_data(username, password):
    """Log in the user and fetch client data."""
    session_token = login_user(username, password)
    if session_token:
        return get_client_data(session_token)
    else:
        raise Exception("<PERSON><PERSON> failed, unable to retrieve client data.")