"""
Benchmarking script for IB Options Trading application.
This script measures latency for various operations.
"""

import time
import statistics
import logging
import pandas as pd
import matplotlib.pyplot as plt
import math  # Add this import at the top of the file
from ib_insync import IB, util
from ib_options.connect import connect_ib
from ib_options.account import get_account_balances, get_open_option_positions
from ib_options.market_data import get_option_chain, get_option_market_data
from ib_options.orders import create_option_contract, place_option_order, get_order_status, cancel_order

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('IB_Options_Benchmark')

# Benchmark parameters
TEST_SYMBOL = 'SPY'  # Use a liquid ETF for benchmarking
TEST_ITERATIONS = 5  # Number of iterations for each test
TEST_QUANTITY = 1    # Small quantity for testing orders

class LatencyBenchmark:
    def __init__(self):
        self.ib = None
        self.results = {
            'operation': [],
            'iteration': [],
            'latency_ms': []
        }
        self.test_data = {}
    
    def connect(self):
        """Connect to IB"""
        logger.info("Connecting to Interactive Brokers...")
        self.ib = connect_ib(port=7497, client_id=1)
        if not self.ib or not self.ib.isConnected():
            logger.error("Failed to connect to Interactive Brokers")
            return False
        return True
    
    def benchmark_operation(self, name, operation, *args):
        """Benchmark a single operation multiple times"""
        logger.info(f"Benchmarking {name}...")
        
        latencies = []
        for i in range(TEST_ITERATIONS):
            start_time = time.time()
            result = operation(*args)
            end_time = time.time()
            
            latency_ms = (end_time - start_time) * 1000
            latencies.append(latency_ms)
            
            self.results['operation'].append(name)
            self.results['iteration'].append(i+1)
            self.results['latency_ms'].append(latency_ms)
            
            logger.info(f"  Iteration {i+1}: {latency_ms:.2f} ms")
            
            # Store the last result for potential use in subsequent tests
            if i == TEST_ITERATIONS - 1 and result:
                self.test_data[name] = result
            
            # Small delay between iterations
            time.sleep(0.5)
        
        # Calculate statistics
        avg_latency = statistics.mean(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)
        std_dev = statistics.stdev(latencies) if len(latencies) > 1 else 0
        
        logger.info(f"  Results for {name}:")
        logger.info(f"    Average: {avg_latency:.2f} ms")
        logger.info(f"    Min: {min_latency:.2f} ms")
        logger.info(f"    Max: {max_latency:.2f} ms")
        logger.info(f"    Std Dev: {std_dev:.2f} ms")
        
        return avg_latency
    
    def prepare_order_test_data(self):
        """Prepare data needed for order tests"""
        # Get option chain
        chains = self.test_data.get('Option Chain')
        if not chains:
            logger.error("No option chain data available for order tests")
            return False
            
        chain = next(iter(chains))
        expiry = sorted(chain.expirations)[0]
        valid_strikes = sorted(chain.strikes)
        
        # Get current stock price
        try:
            stock = Stock(TEST_SYMBOL, 'SMART', 'USD')
            self.ib.qualifyContracts(stock)
            ticker = self.ib.reqMktData(stock)
            self.ib.sleep(1)  # Wait for data to arrive
            current_price = ticker.last if hasattr(ticker, 'last') and ticker.last > 0 else valid_strikes[len(valid_strikes)//2]
            
            # Find a valid strike that's far OTM (approximately 30% below current price)
            target_strike = round(current_price * 0.7, 1)  # 30% OTM
            
            # Find the closest valid strike to our target
            far_otm_strike = min(valid_strikes, key=lambda x: abs(x - target_strike))
            logger.info(f"Using strike price {far_otm_strike} (closest to target {target_strike})")
            
            self.test_data['expiry'] = expiry
            self.test_data['strike'] = far_otm_strike
            
            return True
        except Exception as e:
            logger.error(f"Error preparing order test data: {str(e)}")
            return False
    
    def run_benchmarks(self):
        """Run all benchmarks"""
        if not self.connect():
            return False
        
        try:
            # Benchmark 1: Account Balances
            self.benchmark_operation('Account Balances', get_account_balances, self.ib)
            
            # Benchmark 2: Option Positions
            self.benchmark_operation('Option Positions', get_open_option_positions, self.ib)
            
            # Benchmark 3: Option Chain
            self.benchmark_operation('Option Chain', get_option_chain, self.ib, TEST_SYMBOL)
            
            # Prepare data for order tests
            if not self.prepare_order_test_data():
                logger.error("Cannot proceed with order benchmarks")
                return False
            
            # Benchmark 4: Option Market Data
            self.benchmark_operation('Market Data (Call)', get_option_market_data, 
                                    self.ib, TEST_SYMBOL, self.test_data['expiry'], 
                                    self.test_data['strike'], 'C')
            
            self.benchmark_operation('Market Data (Put)', get_option_market_data, 
                                    self.ib, TEST_SYMBOL, self.test_data['expiry'], 
                                    self.test_data['strike'], 'P')
            
            # Create contract for order tests
            contract = create_option_contract(TEST_SYMBOL, self.test_data['expiry'], 
                                             self.test_data['strike'], 'P')
            
            # Get market data to set a low limit price
            market_data = get_option_market_data(self.ib, TEST_SYMBOL, self.test_data['expiry'], 
                                               self.test_data['strike'], 'P')
            if not market_data or market_data['bid'] <= 0:
                limit_price = 0.01  # Minimum price
            else:
                limit_price = max(0.01, market_data['bid'] * 0.5)  # Half the current bid
            
            # Benchmark 5: Place Order
            # Note: We'll only do this once to avoid creating too many orders
            logger.info("Benchmarking Order Placement (single test)...")
            start_time = time.time()
            trade = place_option_order(self.ib, contract, 'BUY', TEST_QUANTITY, 'LMT', limit_price)
            end_time = time.time()
            order_latency = (end_time - start_time) * 1000
            
            self.results['operation'].append('Place Order')
            self.results['iteration'].append(1)
            self.results['latency_ms'].append(order_latency)
            
            logger.info(f"  Order Placement: {order_latency:.2f} ms")
            
            if trade:
                self.test_data['trade'] = trade
                
                # Benchmark 6: Order Status
                self.ib.sleep(1)  # Wait for order to be processed
                self.benchmark_operation('Order Status', get_order_status, self.ib, trade)
                
                # Benchmark 7: Cancel Order
                # Note: We'll only do this once
                logger.info("Benchmarking Order Cancellation (single test)...")
                start_time = time.time()
                cancel_result = cancel_order(self.ib, trade)
                end_time = time.time()
                cancel_latency = (end_time - start_time) * 1000
                
                self.results['operation'].append('Cancel Order')
                self.results['iteration'].append(1)
                self.results['latency_ms'].append(cancel_latency)
                
                logger.info(f"  Order Cancellation: {cancel_latency:.2f} ms")
            
            # Create DataFrame and save results
            self.save_results()
            
            return True
            
        except Exception as e:
            logger.error(f"Benchmark failed with exception: {str(e)}")
            return False
        finally:
            # Disconnect
            if self.ib and self.ib.isConnected():
                self.ib.disconnect()
                logger.info("Disconnected from Interactive Brokers")
    
    def save_results(self):
        """Save benchmark results to CSV and generate plots"""
        # Create DataFrame
        df = pd.DataFrame(self.results)
        
        # Save to CSV
        df.to_csv('ib_options_benchmark_results.csv', index=False)
        logger.info("Benchmark results saved to ib_options_benchmark_results.csv")
        
        # Generate summary statistics
        summary = df.groupby('operation')['latency_ms'].agg(['mean', 'min', 'max', 'std'])
        summary.to_csv('ib_options_benchmark_summary.csv')
        logger.info("Benchmark summary saved to ib_options_benchmark_summary.csv")
        
        # Generate plots
        self.generate_plots(df, summary)
    
    def generate_plots(self, df, summary):
        """Generate benchmark visualization plots"""
        # Bar chart of average latencies
        plt.figure(figsize=(12, 6))
        summary['mean'].plot(kind='bar', yerr=summary['std'], capsize=5)
        plt.title('Average Latency by Operation')
        plt.ylabel('Latency (ms)')
        plt.xlabel('Operation')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.savefig('ib_options_benchmark_avg_latency.png')
        
        # Box plot for operations with multiple iterations
        operations_with_iterations = df['operation'].value_counts()
        operations_with_iterations = operations_with_iterations[operations_with_iterations > 1].index.tolist()
        
        if operations_with_iterations:
            plt.figure(figsize=(12, 6))
            df_multi = df[df['operation'].isin(operations_with_iterations)]
            df_multi.boxplot(column='latency_ms', by='operation', figsize=(12, 6))
            plt.title('Latency Distribution by Operation')
            plt.suptitle('')  # Remove default title
            plt.ylabel('Latency (ms)')
            plt.xlabel('Operation')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.savefig('ib_options_benchmark_latency_distribution.png')
        
        logger.info("Benchmark plots generated")

if __name__ == "__main__":
    benchmark = LatencyBenchmark()
    benchmark.run_benchmarks()



