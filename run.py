from ib_options.main import ensure_log_directory, display_menu, handle_menu_choice
from ib_options.connect import connect_ib
from ib_options.market_data import check_market_data_permissions
import sys

def main():
    # Ensure logs directory exists
    ensure_log_directory()

    # Connect to IB TWS/Gateway (paper trading)
    print("Connecting to Interactive Brokers paper trading account...")
    ib = connect_ib(port=7497, client_id=1)

    if ib:
        print("Connected to Interactive Brokers successfully!")

        # Check market data permissions on startup
        print("\nChecking market data permissions...")
        permissions = check_market_data_permissions(ib)
        if permissions.get('delayed'):
            print("✓ Delayed market data is available (free)")
        else:
            print("⚠ No market data permissions detected")

        if permissions.get('real_time'):
            print("✓ Real-time market data is available")
        else:
            print("ℹ Real-time market data requires subscription")

        try:
            running = True
            while running:
                choice = display_menu()
                if choice == '10':  # Exit option (updated from 8 to 10)
                    running = False
                else:
                    handle_menu_choice(ib, choice)

                    # Small pause before showing menu again
                    if running:
                        input("\nPress Enter to continue...")

        except KeyboardInterrupt:
            print("\nProgram interrupted by user")
        finally:
            ib.disconnect()
            print("Disconnected from Interactive Brokers")
    else:
        print("Failed to connect to Interactive Brokers. Please check if TWS/Gateway is running.")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
