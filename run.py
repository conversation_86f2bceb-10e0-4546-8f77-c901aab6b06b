from ib_options.main import ensure_log_directory, display_menu, handle_menu_choice
from ib_options.connect import connect_ib
import sys

def main():
    # Ensure logs directory exists
    ensure_log_directory()
    
    # Connect to IB TWS/Gateway (paper trading)
    print("Connecting to Interactive Brokers paper trading account...")
    ib = connect_ib(port=7497, client_id=1)
    
    if ib:
        try:
            running = True
            while running:
                choice = display_menu()
                if choice == '8':  # Exit option
                    running = False
                else:
                    handle_menu_choice(ib, choice)
                    
                    # Small pause before showing menu again
                    if running:
                        input("\nPress Enter to continue...")
                        
        except KeyboardInterrupt:
            print("\nProgram interrupted by user")
        finally:
            ib.disconnect()
            print("Disconnected from Interactive Brokers")
    else:
        print("Failed to connect to Interactive Brokers. Please check if TWS/Gateway is running.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
