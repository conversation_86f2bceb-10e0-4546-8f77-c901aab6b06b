"""
Automated test script for IB Options Trading application.
This script tests all main functionalities (options 1-8) automatically.
"""

import time
import logging
import math  # Add this import at the top of the file
from ib_insync import IB, util, Stock
from ib_options.connect import connect_ib, setup_logger
from ib_options.account import get_account_balances, get_open_option_positions
from ib_options.market_data import get_option_chain, get_option_market_data
from ib_options.orders import create_option_contract, place_option_order, get_order_status, cancel_order

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('IB_Options_Test')

# Test parameters
TEST_SYMBOL = 'SPY'  # Use a liquid ETF for testing
TEST_QUANTITY = 1    # Small quantity for testing

def test_connection():
    """Test 1: Connection to IB"""
    logger.info("TEST 1: Testing connection to Interactive Brokers...")
    ib = connect_ib(port=7497, client_id=1)
    
    if ib and ib.isConnected():
        logger.info("✓ Connection successful")
        return ib
    else:
        logger.error("✗ Connection failed")
        return None

def test_account_balances(ib):
    """Test 2: Fetch account balances"""
    logger.info("TEST 2: Fetching account balances...")
    balances = get_account_balances(ib)
    
    if balances and isinstance(balances, dict) and len(balances) > 0:
        logger.info(f"✓ Account balances retrieved: {len(balances)} items")
        logger.info(f"Sample balance: {next(iter(balances.items()))}")
        return True
    else:
        logger.error("✗ Failed to retrieve account balances")
        return False

def test_option_positions(ib):
    """Test 3: Fetch option positions"""
    logger.info("TEST 3: Fetching option positions...")
    positions = get_open_option_positions(ib)
    
    if positions is not None:
        logger.info(f"✓ Option positions retrieved: {len(positions)} positions")
        return True
    else:
        logger.error("✗ Failed to retrieve option positions")
        return False

def test_option_chain(ib):
    """Test 4: Fetch option chain"""
    logger.info(f"TEST 4: Fetching option chain for {TEST_SYMBOL}...")
    chains = get_option_chain(ib, TEST_SYMBOL)
    
    if chains:
        chain = next(iter(chains))
        logger.info(f"✓ Option chain retrieved for {TEST_SYMBOL}")
        logger.info(f"Exchange: {chain.exchange}")
        logger.info(f"Expirations: {len(chain.expirations)} available")
        logger.info(f"Strikes: {len(chain.strikes)} available")
        
        # Return the first expiration and a middle strike for further testing
        expiry = sorted(chain.expirations)[0]
        strike = sorted(chain.strikes)[len(chain.strikes)//2]
        return True, expiry, strike
    else:
        logger.error(f"✗ Failed to retrieve option chain for {TEST_SYMBOL}")
        return False, None, None

def test_option_market_data(ib, expiry, strike):
    """Test 5: Fetch option market data"""
    logger.info(f"TEST 5: Fetching market data for {TEST_SYMBOL} option...")
    
    # Test both call and put
    call_data = get_option_market_data(ib, TEST_SYMBOL, expiry, strike, 'C')
    put_data = get_option_market_data(ib, TEST_SYMBOL, expiry, strike, 'P')
    
    if call_data and put_data:
        logger.info(f"✓ Market data retrieved for {TEST_SYMBOL} options")
        logger.info(f"Call - Bid: {call_data['bid']}, Ask: {call_data['ask']}")
        logger.info(f"Put - Bid: {put_data['bid']}, Ask: {put_data['ask']}")
        return True
    else:
        logger.error(f"✗ Failed to retrieve market data for {TEST_SYMBOL} options")
        return False

def test_option_order_placement(ib, expiry, strike):
    """Test 6: Place option order"""
    logger.info(f"TEST 6: Placing limit order for {TEST_SYMBOL} option...")
    
    try:
        # Get the option chain to find valid strikes
        chains = get_option_chain(ib, TEST_SYMBOL)
        if not chains:
            logger.error("Could not retrieve option chain for order placement")
            return False, None
            
        chain = next(iter(chains))
        valid_strikes = sorted(chain.strikes)
        
        # Create a far OTM put option to minimize risk (very cheap)
        # Find a strike that's at least 10% below current price
        stock = Stock(TEST_SYMBOL, 'SMART', 'USD')
        ib.qualifyContracts(stock)
        ticker = ib.reqMktData(stock)
        ib.sleep(1)  # Wait for data to arrive
        current_price = ticker.last if hasattr(ticker, 'last') and ticker.last > 0 else strike
        
        # Find a valid strike that's far OTM (approximately 30% below current price)
        target_strike = round(current_price * 0.7, 1)  # 30% OTM
        
        # Find the closest valid strike to our target
        far_otm_strike = min(valid_strikes, key=lambda x: abs(x - target_strike))
        logger.info(f"Using strike price {far_otm_strike} (closest to target {target_strike})")
        
        # Create contract and place a limit buy order with a very low price (unlikely to be filled)
        contract = create_option_contract(TEST_SYMBOL, expiry, far_otm_strike, 'P')
        
        # Set a very low limit price
        limit_price = 0.01  # Minimum price
        
        # Try to get market data if available
        try:
            market_data = get_option_market_data(ib, TEST_SYMBOL, expiry, far_otm_strike, 'P')
            if market_data and market_data.get('bid', 0) > 0:
                limit_price = max(0.01, market_data['bid'] * 0.5)  # Half the current bid (unlikely to fill)
        except Exception as e:
            logger.warning(f"Could not get market data for limit price: {str(e)}")
        
        trade = place_option_order(ib, contract, 'BUY', TEST_QUANTITY, 'LMT', limit_price)
        
        if trade:
            logger.info(f"✓ Order placed successfully. Order ID: {trade.order.orderId}")
            return True, trade
        else:
            logger.error("✗ Failed to place order")
            return False, None
    except Exception as e:
        logger.error(f"Error in order placement test: {str(e)}")
        return False, None

def test_order_status(ib, trade):
    """Test 7: Check order status"""
    logger.info("TEST 7: Checking order status...")
    
    if not trade:
        logger.error("✗ No trade object available to check status")
        return False
    
    # Wait a moment for the order to be processed
    ib.sleep(2)
    
    status = get_order_status(ib, trade)
    if status:
        logger.info(f"✓ Order status retrieved: {status['status']}")
        return True
    else:
        logger.error("✗ Failed to retrieve order status")
        return False

def test_cancel_order(ib, trade):
    """Test 8: Cancel order"""
    logger.info("TEST 8: Cancelling order...")
    
    if not trade:
        logger.error("✗ No trade object available to cancel")
        return False
    
    result = cancel_order(ib, trade)
    if result:
        logger.info(f"✓ Order cancelled successfully")
        
        # Verify cancellation
        ib.sleep(1)
        status = get_order_status(ib, trade)
        if status and status['status'] == 'Cancelled':
            logger.info("✓ Order status confirmed as Cancelled")
            return True
        else:
            logger.warning("⚠ Order cancellation not confirmed in status")
            return False
    else:
        logger.error("✗ Failed to cancel order")
        return False

def run_all_tests():
    """Run all tests in sequence"""
    logger.info("Starting IB Options functionality tests...")
    
    # Test 1: Connection
    ib = test_connection()
    if not ib:
        logger.error("Cannot proceed with tests due to connection failure")
        return False
    
    try:
        # Test 2: Account balances
        test_account_balances(ib)
        
        # Test 3: Option positions
        test_option_positions(ib)
        
        # Test 4: Option chain
        chain_result, expiry, strike = test_option_chain(ib)
        if not chain_result:
            logger.error("Cannot proceed with market data and order tests")
            return False
        
        # Test 5: Option market data
        test_option_market_data(ib, expiry, strike)
        
        # Test 6: Place option order
        order_result, trade = test_option_order_placement(ib, expiry, strike)
        
        # Test 7: Order status
        if order_result:
            test_order_status(ib, trade)
        
        # Test 8: Cancel order
        if order_result:
            test_cancel_order(ib, trade)
        
        logger.info("All tests completed")
        return True
        
    except Exception as e:
        logger.error(f"Test failed with exception: {str(e)}")
        return False
    finally:
        # Disconnect
        if ib.isConnected():
            ib.disconnect()
            logger.info("Disconnected from Interactive Brokers")

if __name__ == "__main__":
    run_all_tests()


