#!/usr/bin/env python3
"""
Demo: 5-second streaming option chain data
"""

import time
import os
from datetime import datetime
from ib_options import IBOptionsClient, OptionContract, OptionRight

def demo_streaming():
    print("🔴 DEMO: 5-Second Option Chain Streaming")
    print("=" * 50)
    
    client = IBOptionsClient()
    
    try:
        print("🔌 Connecting to IB...")
        client.connect()
        time.sleep(2)
        
        if not client.is_connected():
            print("❌ Failed to connect")
            return
        
        # Demo with SPY options
        symbol = "SPY"
        expiry = "20250611"
        strikes = [595, 600, 605]  # 3 strikes for demo
        
        print(f"📊 Streaming {symbol} options for {expiry}")
        print(f"🎯 Strikes: {strikes}")
        print("⏱️  Duration: 5 seconds")
        print("\n🔴 STARTING STREAM...")
        
        # Create contracts
        contracts = []
        for strike in strikes:
            contracts.append(OptionContract(
                symbol=symbol,
                expiry=expiry,
                strike=strike,
                right=OptionRight.CALL
            ))
        
        # Get initial market data for each contract
        market_data = {}
        for contract in contracts:
            data = client.get_option_market_data(contract)
            if data:
                key = f"{contract.strike}C"
                market_data[key] = data
        
        # Simulate streaming by refreshing data every second
        for second in range(5):
            # Clear screen
            os.system('cls' if os.name == 'nt' else 'clear')
            
            print(f"🔴 LIVE SPY OPTION CHAIN - {expiry} - {datetime.now().strftime('%H:%M:%S')}")
            print("=" * 70)
            print(f"{'Strike':<8} {'Type':<4} {'Bid':<8} {'Ask':<8} {'Last':<8} {'Volume':<10}")
            print("-" * 70)
            
            # Refresh data
            for contract in contracts:
                data = client.get_option_market_data(contract)
                if data:
                    key = f"{contract.strike}C"
                    market_data[key] = data
                    
                    # Display data
                    bid_str = f"${data.bid:.2f}" if data.bid > 0 else "-"
                    ask_str = f"${data.ask:.2f}" if data.ask > 0 else "-"
                    last_str = f"${data.last:.2f}" if data.last > 0 else "-"
                    vol_str = f"{int(data.volume):,}" if data.volume > 0 else "-"
                    
                    print(f"{contract.strike:<8.0f} {'C':<4} {bid_str:<8} {ask_str:<8} {last_str:<8} {vol_str:<10}")
            
            print("-" * 70)
            print(f"📊 Second {second + 1}/5 | Data Type: {list(market_data.values())[0].data_type if market_data else 'N/A'}")
            print("🔴 LIVE STREAMING... (Press Ctrl+C to stop)")
            
            # Wait 1 second before next update
            if second < 4:  # Don't wait after last iteration
                time.sleep(1)
        
        print(f"\n⏹️  Demo completed! Streamed for 5 seconds")
        print(f"📊 Total contracts monitored: {len(contracts)}")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Demo stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        client.disconnect()
        print("🔌 Disconnected")

if __name__ == "__main__":
    demo_streaming()
