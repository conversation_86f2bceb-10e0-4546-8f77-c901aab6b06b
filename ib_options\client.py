"""
Core IB Options Trading Client
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta
import asyncio
import time
import math
from concurrent.futures import ThreadPoolExecutor
import threading
import gc
import psutil
import os
from contextlib import contextmanager

from ib_insync import IB, Stock, Option, MarketOrder, LimitOrder
from .connect import connection_manager, setup_logger
from .models import (
    OptionContract, MarketData, OptionChain, Order, OrderStatus, 
    Position, AccountBalance, OptionRight, OrderType, OrderAction
)
from .config import config

class IBOptionsClient:
    """Main client for IB Options Trading operations"""
    
    def __init__(self):
        self.logger = setup_logger('IBOptionsClient')
        self.ib = None
        self.market_data_cache = {}
        self.cache_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=10)
        
    def connect(self) -> bool:
        """Connect to Interactive Brokers"""
        try:
            self.ib = connection_manager.connect()
            if self.ib:
                self.logger.info("Client connected successfully")
                return True
            else:
                self.logger.error("Failed to connect client")
                return False
        except Exception as e:
            self.logger.error(f"Error connecting client: {e}")
            return False
    
    def disconnect(self) -> bool:
        """Disconnect from Interactive Brokers"""
        if self.ib and self.ib.isConnected():
            try:
                self.ib.disconnect()
                self.logger.info("Client disconnected successfully")
                return True
            except Exception as e:
                self.logger.error(f"Error disconnecting client: {e}")
                return False
        return True
    
    def is_connected(self) -> bool:
        """Check if client is connected"""
        return self.ib is not None and self.ib.isConnected()
    
    def get_account_balances(self) -> Optional[AccountBalance]:
        """Get account balances"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None
        
        try:
            account_values = self.ib.accountValues()
            
            # Extract relevant values
            balances = {}
            for value in account_values:
                if value.currency == 'USD' and value.account == self.ib.client.account:
                    balances[value.tag] = float(value.value) if value.value.replace('.', '', 1).isdigit() else value.value
            
            # Create AccountBalance object
            return AccountBalance(
                account_id=self.ib.client.account,
                net_liquidation=balances.get('NetLiquidation', 0.0),
                equity_with_loan=balances.get('EquityWithLoanValue', 0.0),
                previous_day_equity=balances.get('PreviousDayEquityWithLoanValue', 0.0),
                buying_power=balances.get('BuyingPower', 0.0),
                available_funds=balances.get('AvailableFunds', 0.0),
                maintenance_margin=balances.get('MaintMarginReq', 0.0),
                initial_margin=balances.get('InitMarginReq', 0.0),
                excess_liquidity=balances.get('ExcessLiquidity', 0.0),
                cushion=balances.get('Cushion', 0.0),
                full_initial_margin=balances.get('FullInitMarginReq', 0.0),
                full_maintenance_margin=balances.get('FullMaintMarginReq', 0.0),
                gross_position_value=balances.get('GrossPositionValue', 0.0),
                cash_balance=balances.get('TotalCashValue', 0.0)
            )
        except Exception as e:
            self.logger.error(f"Error getting account balances: {e}")
            return None
    
    def get_option_positions(self) -> Optional[List[Position]]:
        """Get open option positions"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None
        
        try:
            portfolio = self.ib.portfolio()
            
            positions = []
            for item in portfolio:
                if item.contract.secType == 'OPT':
                    # Create Position object
                    position = Position(
                        symbol=item.contract.symbol,
                        contract_id=item.contract.conId,
                        expiry=item.contract.lastTradeDateOrContractMonth,
                        strike=item.contract.strike,
                        right=OptionRight.CALL if item.contract.right == 'C' else OptionRight.PUT,
                        quantity=item.position,
                        market_price=item.marketPrice,
                        market_value=item.marketValue,
                        average_cost=item.averageCost,
                        unrealized_pnl=item.unrealizedPNL,
                        realized_pnl=item.realizedPNL,
                        account_id=item.account
                    )
                    positions.append(position)
            
            return positions
        except Exception as e:
            self.logger.error(f"Error getting option positions: {e}")
            return None
    
    def get_option_chain(self, symbol: str) -> Optional[OptionChain]:
        """Get option chain for a symbol"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None
        
        try:
            self.logger.info(f"Fetching option chain for {symbol}")
            
            # Create stock contract
            stock = Stock(symbol, 'SMART', 'USD')
            
            # Qualify the contract
            qualified = self.ib.qualifyContracts(stock)
            if not qualified:
                self.logger.error(f"Could not qualify stock contract: {symbol}")
                return None
            
            stock = qualified[0]
            
            # Request option chain
            chains = self.ib.reqSecDefOptParams(stock.symbol, '', stock.secType, stock.conId)
            
            if not chains:
                self.logger.error(f"No option chains found for {symbol}")
                return None
            
            # Get the first chain (usually SMART exchange)
            chain = chains[0]
            
            # Create OptionChain object
            return OptionChain(
                symbol=symbol,
                exchange=chain.exchange,
                underlying_price=self._get_underlying_price(symbol),
                expirations=chain.expirations,
                strikes=chain.strikes,
                multiplier=chain.multiplier
            )
        except Exception as e:
            self.logger.error(f"Error getting option chain: {e}")
            return None
    
    def _get_underlying_price(self, symbol: str) -> float:
        """Get current price of underlying"""
        try:
            # Create stock contract
            stock = Stock(symbol, 'SMART', 'USD')
            
            # Qualify the contract
            qualified = self.ib.qualifyContracts(stock)
            if not qualified:
                return 0.0
            
            stock = qualified[0]
            
            # Request market data
            ticker = self.ib.reqMktData(stock)
            
            # Wait for data
            self.ib.sleep(1)
            
            # Get last price or close price
            price = ticker.last if ticker.last > 0 else ticker.close
            
            # Cancel market data to avoid memory leaks
            self.ib.cancelMktData(stock)
            
            return price
        except Exception as e:
            self.logger.error(f"Error getting underlying price: {e}")
            return 0.0
    
    def get_option_market_data(self, contract: Union[OptionContract, Dict]) -> Optional[MarketData]:
        """Get market data for a specific option contract"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None
        
        try:
            # Convert to OptionContract if dict
            if isinstance(contract, dict):
                contract = OptionContract(**contract)
            
            # Validate expiry format
            if contract.expiry and len(contract.expiry) != 8:
                self.logger.error(f"Invalid expiry format: {contract.expiry}. Must be YYYYMMDD (8 digits)")
                return None
            
            # Create cache key
            cache_key = f"{contract.symbol}_{contract.expiry}_{contract.strike}_{contract.right.value}"
            
            # Check cache first
            with self.cache_lock:
                if cache_key in self.market_data_cache:
                    cached_data = self.market_data_cache[cache_key]
                    # Check if cache is still valid
                    if (datetime.now() - cached_data.timestamp).seconds < 60:  # 60 seconds cache
                        return cached_data
            
            try:
                self.logger.debug(f"Fetching market data for {cache_key}")
                
                # Create IB option contract
                ib_contract = Option(
                    contract.symbol, 
                    contract.expiry, 
                    contract.strike, 
                    contract.right.value, 
                    contract.exchange
                )
                
                # Qualify contract
                qualified = self.ib.qualifyContracts(ib_contract)
                if not qualified:
                    self.logger.error(f"Could not qualify contract: {contract}")
                    return None
                
                ib_contract = qualified[0]
                
                # Request market data
                ticker = self.ib.reqMktData(
                    ib_contract, 
                    '', 
                    False, 
                    False
                )
                
                # Wait for data
                self.ib.sleep(1)
                
                # Create MarketData object
                market_data = MarketData(
                    symbol=contract.symbol,
                    expiry=contract.expiry,
                    strike=contract.strike,
                    right=contract.right,
                    bid=ticker.bid if hasattr(ticker, 'bid') and ticker.bid else 0,
                    ask=ticker.ask if hasattr(ticker, 'ask') and ticker.ask else 0,
                    last=ticker.last if hasattr(ticker, 'last') and ticker.last else 0,
                    volume=ticker.volume if hasattr(ticker, 'volume') and ticker.volume else 0,
                    open_interest=ticker.openInterest if hasattr(ticker, 'openInterest') and ticker.openInterest else 0,
                    underlying_price=self._get_underlying_price(contract.symbol),
                    implied_volatility=ticker.impliedVolatility if hasattr(ticker, 'impliedVolatility') and ticker.impliedVolatility else 0,
                    delta=ticker.delta if hasattr(ticker, 'delta') and ticker.delta else 0,
                    gamma=ticker.gamma if hasattr(ticker, 'gamma') and ticker.gamma else 0,
                    theta=ticker.theta if hasattr(ticker, 'theta') and ticker.theta else 0,
                    vega=ticker.vega if hasattr(ticker, 'vega') and ticker.vega else 0,
                    timestamp=datetime.now()
                )
                
                # Cache the data
                with self.cache_lock:
                    self.market_data_cache[cache_key] = market_data
                
                # Cancel market data to avoid memory leaks
                self.ib.cancelMktData(ib_contract)
                
                return market_data
            except Exception as e:
                self.logger.error(f"Error fetching market data: {e}")
                return None
        except Exception as e:
            self.logger.error(f"Error in get_option_market_data: {e}")
            return None
    
    def place_option_order(self, order: Order) -> Optional[int]:
        """Place an option order"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None

        try:
            self.logger.info(f"Placing order: {order.action.value} {order.quantity} {order.contract.symbol} {order.contract.right.value} {order.contract.strike} {order.contract.expiry}")

            # Create IB option contract
            ib_contract = Option(
                order.contract.symbol,
                order.contract.expiry,
                order.contract.strike,
                order.contract.right.value,
                order.contract.exchange
            )

            # Qualify contract
            qualified = self.ib.qualifyContracts(ib_contract)
            if not qualified:
                self.logger.error(f"Could not qualify contract for order: {order.contract}")
                return None

            # Create IB order
            if order.order_type == OrderType.MARKET:
                ib_order = MarketOrder(
                    order.action.value,
                    order.quantity
                )
            elif order.order_type == OrderType.LIMIT:
                if order.limit_price is None:
                    self.logger.error("Limit price is required for limit orders")
                    return None
                
                ib_order = LimitOrder(
                    order.action.value,
                    order.quantity,
                    order.limit_price
                )
            else:
                self.logger.error(f"Unsupported order type: {order.order_type}")
                return None

            # Place order
            trade = self.ib.placeOrder(qualified[0], ib_order)

            if trade:
                order_id = trade.order.orderId
                self.logger.info(f"Order placed successfully with ID: {order_id}")
                return order_id
            else:
                self.logger.error("Failed to place order")
                return None

        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            return None

    def get_order_status(self, order_id: Optional[int] = None) -> Union[OrderStatus, List[OrderStatus], None]:
        """Get status of an order or all orders"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None
        
        try:
            # Get all open orders
            open_orders = self.ib.openOrders()
            
            if order_id is not None:
                # Find specific order
                for trade in self.ib.openTrades():
                    if trade.order.orderId == order_id:
                        return OrderStatus(
                            order_id=trade.order.orderId,
                            status=trade.orderStatus.status,
                            filled=trade.orderStatus.filled,
                            remaining=trade.orderStatus.remaining,
                            avg_fill_price=trade.orderStatus.avgFillPrice,
                            last_fill_price=trade.orderStatus.lastFillPrice,
                            why_held=trade.orderStatus.whyHeld
                        )
                return None
            else:
                # Return all orders
                statuses = []
                for trade in self.ib.openTrades():
                    statuses.append(OrderStatus(
                        order_id=trade.order.orderId,
                        status=trade.orderStatus.status,
                        filled=trade.orderStatus.filled,
                        remaining=trade.orderStatus.remaining,
                        avg_fill_price=trade.orderStatus.avgFillPrice,
                        last_fill_price=trade.orderStatus.lastFillPrice,
                        why_held=trade.orderStatus.whyHeld
                    ))
                return statuses
        except Exception as e:
            self.logger.error(f"Error getting order status: {e}")
            return None
    
    def cancel_order(self, order_id: int) -> bool:
        """Cancel an order"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return False
        
        try:
            # Find the order
            for trade in self.ib.openTrades():
                if trade.order.orderId == order_id:
                    # Cancel the order
                    self.ib.cancelOrder(trade.order)
                    self.logger.info(f"Order {order_id} canceled")
                    return True
            
            self.logger.error(f"Order {order_id} not found")
            return False
        except Exception as e:
            self.logger.error(f"Error canceling order: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources"""
        try:
            # Disconnect from IB
            self.disconnect()
            
            # Clear cache
            with self.cache_lock:
                self.market_data_cache.clear()
            
            # Shutdown executor
            self.executor.shutdown(wait=False)
            
            # Force garbage collection
            gc.collect()
            
            self.logger.info("Resources cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    def get_available_symbols(self, asset_class: str = "STK") -> List[str]:
        """Get available symbols for trading (placeholder implementation)"""
        # This would typically require market data subscriptions
        # For now, return common option symbols
        common_symbols = [
            "SPY", "QQQ", "IWM", "EEM", "GLD", "SLV", "TLT", "XLF", "XLE", "XLK",
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "AMD", "INTC"
        ]

        self.logger.info(f"Returning {len(common_symbols)} common option symbols")
        return common_symbols

    def clear_cache(self):
        """Clear market data cache"""
        with self.cache_lock:
            self.market_data_cache.clear()
        self.logger.info("Market data cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.cache_lock:
            return {
                "cache_size": len(self.market_data_cache),
                "cache_keys": list(self.market_data_cache.keys())
            }

    def get_valid_option_contract(self, symbol: str, days_to_expiry: int = 30) -> Optional[OptionContract]:
        """Get a valid option contract for testing purposes"""
        try:
            # Get option chain first
            chain = self.get_option_chain(symbol)
            if not chain or not chain.expirations or not chain.strikes:
                return None

            # Find an expiry that's at least days_to_expiry in the future
            from datetime import datetime, timedelta
            target_date = datetime.now() + timedelta(days=days_to_expiry)
            target_date_str = target_date.strftime('%Y%m%d')

            # Find the nearest expiry that's after our target date
            valid_expiry = None
            for expiry in sorted(chain.expirations):
                if expiry >= target_date_str:
                    valid_expiry = expiry
                    break

            if not valid_expiry:
                # Use the furthest expiry if none are far enough
                valid_expiry = sorted(chain.expirations)[-1]

            # Find a reasonable strike (close to current price if available)
            strikes = sorted(chain.strikes)
            if chain.underlying_price > 0:
                # Find strike closest to current price
                strike = min(strikes, key=lambda x: abs(x - chain.underlying_price))
            else:
                # Use middle strike
                strike = strikes[len(strikes) // 2]

            # Create and validate the contract
            contract = OptionContract(
                symbol=symbol,
                expiry=valid_expiry,
                strike=strike,
                right=OptionRight.CALL
            )

            # Try to qualify the contract to make sure it's valid
            ib_contract = Option(
                contract.symbol,
                contract.expiry,
                contract.strike,
                contract.right.value,
                contract.exchange
            )

            qualified = self.ib.qualifyContracts(ib_contract)
            if qualified:
                self.logger.info(f"Found valid contract: {symbol} {contract.right.value} {contract.strike} {contract.expiry}")
                return contract
            else:
                self.logger.warning(f"Could not qualify contract: {contract}")
                return None

        except Exception as e:
            self.logger.error(f"Error finding valid contract: {e}")
            return None



