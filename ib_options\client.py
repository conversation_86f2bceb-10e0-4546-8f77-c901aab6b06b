"""
Core IB Options Trading Client
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
import threading

from ib_insync import IB, Stock, Option, MarketOrder, LimitOrder
from .connect import connection_manager, setup_logger
from .models import (
    OptionContract, MarketData, OptionChain, Order, OrderStatus, 
    Position, AccountBalance, OptionRight, OrderType, OrderAction
)
from .config import config

class IBOptionsClient:
    """Main client for IB Options Trading operations"""
    
    def __init__(self):
        self.logger = setup_logger('IBOptionsClient')
        self.ib: Optional[IB] = None
        self._market_data_cache: Dict[str, MarketData] = {}
        self._cache_lock = threading.Lock()
        self._executor = ThreadPoolExecutor(max_workers=10)
        
    def connect(self) -> bool:
        """Connect to Interactive Brokers"""
        try:
            self.ib = connection_manager.connect()
            if self.ib:
                self.logger.info("Client connected successfully")
                return True
            else:
                self.logger.error("Failed to connect client")
                return False
        except Exception as e:
            self.logger.error(f"Error connecting client: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from Interactive Brokers"""
        try:
            if self.ib:
                connection_manager.disconnect()
                self.ib = None
                self.logger.info("Client disconnected")
        except Exception as e:
            self.logger.error(f"Error disconnecting client: {e}")
    
    def is_connected(self) -> bool:
        """Check if client is connected"""
        return self.ib is not None and self.ib.isConnected()
    
    def get_account_balances(self) -> Optional[AccountBalance]:
        """Get account balances relevant to options trading"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None
        
        try:
            self.logger.info("Fetching account balances")
            account_values = self.ib.accountSummary()
            
            # Create balance object
            balance = AccountBalance()
            
            # Map account values
            value_map = {
                'AvailableFunds': 'available_funds',
                'BuyingPower': 'buying_power',
                'NetLiquidation': 'net_liquidation',
                'OptionMarketValue': 'option_market_value',
                'UnrealizedPnL': 'unrealized_pnl',
                'RealizedPnL': 'realized_pnl',
                'MaintMarginReq': 'maintenance_margin',
                'InitMarginReq': 'initial_margin',
                'ExcessLiquidity': 'excess_liquidity'
            }
            
            for item in account_values:
                if item.tag in value_map:
                    try:
                        setattr(balance, value_map[item.tag], float(item.value))
                    except (ValueError, TypeError):
                        self.logger.warning(f"Could not parse {item.tag}: {item.value}")
            
            self.logger.info("Account balances retrieved successfully")
            return balance
            
        except Exception as e:
            self.logger.error(f"Error fetching account balances: {e}")
            return None
    
    def get_option_positions(self) -> List[Position]:
        """Get current open option positions"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return []
        
        try:
            self.logger.info("Fetching option positions")
            positions = self.ib.positions()
            
            option_positions = []
            for pos in positions:
                if pos.contract.secType == 'OPT':
                    contract = OptionContract(
                        symbol=pos.contract.symbol,
                        expiry=pos.contract.lastTradeDateOrContractMonth,
                        strike=pos.contract.strike,
                        right=OptionRight(pos.contract.right),
                        exchange=pos.contract.exchange,
                        currency=pos.contract.currency,
                        multiplier=pos.contract.multiplier,
                        trading_class=pos.contract.tradingClass,
                        contract_id=pos.contract.conId,
                        local_symbol=pos.contract.localSymbol
                    )
                    
                    position = Position(
                        contract=contract,
                        position=int(pos.position),
                        avg_cost=float(pos.avgCost)
                    )
                    
                    # Try to get current market price
                    try:
                        market_data = self.get_option_market_data(contract)
                        if market_data:
                            position.market_price = market_data.last or market_data.mid_price
                            position.market_value = position.market_price * abs(position.position) * contract.multiplier
                            position.unrealized_pnl = (position.market_price - position.avg_cost) * position.position * contract.multiplier
                    except Exception as e:
                        self.logger.warning(f"Could not get market data for position: {e}")
                    
                    option_positions.append(position)
            
            self.logger.info(f"Retrieved {len(option_positions)} option positions")
            return option_positions
            
        except Exception as e:
            self.logger.error(f"Error fetching option positions: {e}")
            return []
    
    def get_option_chain(self, symbol: str, exchange: str = None) -> Optional[OptionChain]:
        """Get option chain for a symbol"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None
        
        exchange = exchange or config.trading.default_exchange
        
        try:
            self.logger.info(f"Fetching option chain for {symbol}")
            
            # Create stock contract
            stock = Stock(symbol, exchange, config.trading.default_currency)
            self.ib.qualifyContracts(stock)
            
            # Get option parameters
            chains = self.ib.reqSecDefOptParams(stock.symbol, '', stock.secType, stock.conId)
            
            if not chains:
                self.logger.warning(f"No option chains found for {symbol}")
                return None
            
            # Use the first chain (usually the most liquid exchange)
            chain = next(iter(chains))
            
            # Get current stock price
            underlying_price = 0.0
            try:
                ticker = self.ib.reqMktData(stock, '', True, False)  # delayed data
                self.ib.sleep(1)
                underlying_price = ticker.last or ticker.close or 0.0
                self.ib.cancelMktData(stock)
            except Exception as e:
                self.logger.warning(f"Could not get underlying price: {e}")
            
            option_chain = OptionChain(
                symbol=symbol,
                underlying_price=underlying_price,
                expirations=sorted(chain.expirations),
                strikes=sorted(chain.strikes),
                exchange=chain.exchange
            )
            
            self.logger.info(f"Option chain retrieved for {symbol}: {len(option_chain.expirations)} expirations, {len(option_chain.strikes)} strikes")
            return option_chain
            
        except Exception as e:
            self.logger.error(f"Error fetching option chain for {symbol}: {e}")
            return None
    
    def get_option_market_data(self, contract: OptionContract, use_cache: bool = True) -> Optional[MarketData]:
        """Get market data for an option contract"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None
        
        # Create cache key
        cache_key = f"{contract.symbol}_{contract.expiry}_{contract.strike}_{contract.right.value}"
        
        # Check cache first
        if use_cache:
            with self._cache_lock:
                if cache_key in self._market_data_cache:
                    cached_data = self._market_data_cache[cache_key]
                    # Check if cache is still valid
                    if (datetime.now() - cached_data.timestamp).seconds < config.market_data.cache_duration:
                        return cached_data
        
        try:
            self.logger.debug(f"Fetching market data for {cache_key}")
            
            # Create IB option contract
            ib_contract = Option(
                contract.symbol, 
                contract.expiry, 
                contract.strike, 
                contract.right.value, 
                contract.exchange
            )
            
            # Qualify contract
            qualified = self.ib.qualifyContracts(ib_contract)
            if not qualified:
                self.logger.error(f"Could not qualify contract: {contract}")
                return None
            
            ib_contract = qualified[0]
            
            # Request market data
            ticker = self.ib.reqMktData(
                ib_contract, 
                '', 
                config.market_data.use_delayed_data, 
                False
            )
            
            # Wait for data
            timeout = config.market_data.market_data_timeout
            start_time = time.time()
            while time.time() - start_time < timeout:
                self.ib.sleep(0.1)
                if hasattr(ticker, 'bid') or hasattr(ticker, 'ask') or hasattr(ticker, 'last'):
                    break
            
            # Create market data object
            market_data = MarketData(
                contract=contract,
                bid=getattr(ticker, 'bid', 0.0) or 0.0,
                ask=getattr(ticker, 'ask', 0.0) or 0.0,
                last=getattr(ticker, 'last', 0.0) or 0.0,
                close=getattr(ticker, 'close', 0.0) or 0.0,
                volume=int(getattr(ticker, 'volume', 0) or 0),
                open_interest=int(getattr(ticker, 'openInterest', 0) or 0),
                implied_volatility=getattr(ticker, 'impliedVolatility', 0.0) or 0.0,
                data_type="delayed" if config.market_data.use_delayed_data else "real-time"
            )
            
            # Get Greeks if enabled
            if config.market_data.enable_greeks:
                market_data.delta = getattr(ticker, 'delta', 0.0) or 0.0
                market_data.gamma = getattr(ticker, 'gamma', 0.0) or 0.0
                market_data.theta = getattr(ticker, 'theta', 0.0) or 0.0
                market_data.vega = getattr(ticker, 'vega', 0.0) or 0.0
                market_data.rho = getattr(ticker, 'rho', 0.0) or 0.0
            
            # Cancel market data request
            self.ib.cancelMktData(ib_contract)
            
            # Cache the data
            if use_cache:
                with self._cache_lock:
                    self._market_data_cache[cache_key] = market_data
            
            self.logger.debug(f"Market data retrieved for {cache_key}")
            return market_data
            
        except Exception as e:
            self.logger.error(f"Error fetching market data for {contract}: {e}")
            return None

    def place_option_order(self, order: Order) -> Optional[int]:
        """Place an option order"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None

        try:
            self.logger.info(f"Placing order: {order.action.value} {order.quantity} {order.contract.symbol} {order.contract.right.value} {order.contract.strike} {order.contract.expiry}")

            # Create IB option contract
            ib_contract = Option(
                order.contract.symbol,
                order.contract.expiry,
                order.contract.strike,
                order.contract.right.value,
                order.contract.exchange
            )

            # Qualify contract
            qualified = self.ib.qualifyContracts(ib_contract)
            if not qualified:
                self.logger.error(f"Could not qualify contract for order: {order.contract}")
                return None

            ib_contract = qualified[0]

            # Create IB order
            if order.order_type == OrderType.MARKET:
                ib_order = MarketOrder(order.action.value, order.quantity)
            elif order.order_type == OrderType.LIMIT:
                if order.limit_price is None:
                    self.logger.error("Limit price required for limit order")
                    return None
                ib_order = LimitOrder(order.action.value, order.quantity, order.limit_price)
            else:
                self.logger.error(f"Unsupported order type: {order.order_type}")
                return None

            # Set additional order parameters
            ib_order.tif = order.time_in_force
            ib_order.outsideRth = order.outside_rth
            ib_order.hidden = order.hidden

            if order.good_after_time:
                ib_order.goodAfterTime = order.good_after_time
            if order.good_till_date:
                ib_order.goodTillDate = order.good_till_date

            # Place order
            trade = self.ib.placeOrder(ib_contract, ib_order)

            if trade:
                order_id = trade.order.orderId
                self.logger.info(f"Order placed successfully with ID: {order_id}")
                return order_id
            else:
                self.logger.error("Failed to place order")
                return None

        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            return None

    def get_order_status(self, order_id: Optional[int] = None) -> Union[OrderStatus, List[OrderStatus], None]:
        """Get order status(es)"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return None

        try:
            trades = self.ib.trades()

            if order_id is not None:
                # Get specific order status
                trade = next((t for t in trades if t.order.orderId == order_id), None)
                if trade:
                    return self._create_order_status(trade)
                else:
                    self.logger.warning(f"Order {order_id} not found")
                    return None
            else:
                # Get all order statuses
                statuses = []
                for trade in trades:
                    status = self._create_order_status(trade)
                    if status:
                        statuses.append(status)
                return statuses

        except Exception as e:
            self.logger.error(f"Error getting order status: {e}")
            return None

    def cancel_order(self, order_id: int) -> bool:
        """Cancel an order"""
        if not self.is_connected():
            self.logger.error("Not connected to IB")
            return False

        try:
            self.logger.info(f"Cancelling order {order_id}")
            trades = self.ib.trades()
            trade = next((t for t in trades if t.order.orderId == order_id), None)

            if trade:
                self.ib.cancelOrder(trade.order)
                self.logger.info(f"Order {order_id} cancelled successfully")
                return True
            else:
                self.logger.warning(f"Order {order_id} not found")
                return False

        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            return False

    def _create_order_status(self, trade) -> Optional[OrderStatus]:
        """Create OrderStatus from IB trade object"""
        try:
            return OrderStatus(
                order_id=trade.order.orderId,
                status=trade.orderStatus.status,
                filled=int(trade.orderStatus.filled),
                remaining=int(trade.orderStatus.remaining),
                avg_fill_price=float(trade.orderStatus.avgFillPrice or 0),
                last_fill_price=float(trade.orderStatus.lastFillPrice or 0),
                commission=float(getattr(trade.orderStatus, 'commission', 0) or 0),
                warning_text=getattr(trade.orderStatus, 'warningText', '') or ''
            )
        except Exception as e:
            self.logger.error(f"Error creating order status: {e}")
            return None

    def get_available_symbols(self, asset_class: str = "STK") -> List[str]:
        """Get available symbols for trading (placeholder implementation)"""
        # This would typically require market data subscriptions
        # For now, return common option symbols
        common_symbols = [
            "SPY", "QQQ", "IWM", "EEM", "GLD", "SLV", "TLT", "XLF", "XLE", "XLK",
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "AMD", "INTC"
        ]

        self.logger.info(f"Returning {len(common_symbols)} common option symbols")
        return common_symbols

    def clear_cache(self):
        """Clear market data cache"""
        with self._cache_lock:
            self._market_data_cache.clear()
        self.logger.info("Market data cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._cache_lock:
            return {
                "cache_size": len(self._market_data_cache),
                "cache_keys": list(self._market_data_cache.keys())
            }
