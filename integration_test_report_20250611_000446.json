{"test_suite": "IB Options Trading POC Integration Test", "phase": "Phase 1: Python POC Development", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "timestamp": "2025-06-11T00:04:46.438924", "duration_seconds": 15.184554, "tests_passed": 10, "tests_total": 10, "success_rate": 100.0, "results": {"API Connection & Authentication": {"status": "PASS", "timestamp": "2025-06-11T00:04:31.479391"}, "Account Balances": {"status": "PASS", "timestamp": "2025-06-11T00:04:31.721253"}, "Open Options Positions": {"status": "PASS", "timestamp": "2025-06-11T00:04:31.970278"}, "Option Chains & Available Symbols": {"status": "PASS", "timestamp": "2025-06-11T00:04:34.720669"}, "Option Market Data": {"status": "PASS", "timestamp": "2025-06-11T00:04:37.808199"}, "Market Order Placement": {"status": "PASS", "timestamp": "2025-06-11T00:04:41.672487"}, "Limit Order Placement": {"status": "PASS", "timestamp": "2025-06-11T00:04:45.645187"}, "Order Status Tracking": {"status": "PASS", "timestamp": "2025-06-11T00:04:45.646188"}, "Error Handling & Logging": {"status": "PASS", "timestamp": "2025-06-11T00:04:46.437925"}, "Performance & Monitoring": {"status": "PASS", "timestamp": "2025-06-11T00:04:46.438924"}}, "configuration": {"ib_connection": {"host": "127.0.0.1", "port": 7497, "client_id": 1, "timeout": 30, "max_retries": 3, "retry_delay": 1.0}, "trading": {"default_exchange": "SMART", "default_currency": "USD", "max_order_quantity": 1000, "default_order_timeout": 300, "enable_paper_trading": true, "risk_check_enabled": true}, "market_data": {"use_delayed_data": true, "market_data_timeout": 10, "max_concurrent_requests": 50, "cache_duration": 60, "enable_greeks": true}, "logging": {"log_level": "INFO", "log_dir": "logs", "log_file_prefix": "ib_options", "max_log_files": 30, "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "enable_console_logging": true, "enable_file_logging": true}, "performance": {"enable_metrics": true, "metrics_interval": 60, "enable_benchmarking": false, "max_memory_usage_mb": 512, "enable_profiling": false}}}