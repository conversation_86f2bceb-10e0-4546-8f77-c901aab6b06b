#!/usr/bin/env python3
"""
Test market data with valid SPY contract
"""

from ib_options import IBOptionsClient, OptionContract, OptionRight
import time

def test_valid_contract():
    print("🧪 TESTING VALID SPY CONTRACT")
    print("=" * 50)
    
    # Create client and connect
    client = IBOptionsClient()
    try:
        print("🔌 Connecting to IB...")
        client.connect()
        time.sleep(2)
        
        if not client.is_connected():
            print("❌ Failed to connect to IB")
            return
        
        # Test with the recommended valid contract
        print("📊 Testing SPY C 598 20250610...")
        
        contract = OptionContract(
            symbol='SPY',
            expiry='20250610',
            strike=598.0,
            right=OptionRight.CALL
        )
        
        print(f"📈 Contract: {contract.symbol} ${contract.strike} {contract.right.value} {contract.expiry}")
        
        # Try to get market data
        market_data = client.get_option_market_data(contract)
        
        if market_data:
            print("✅ Market data retrieved!")
            print(f"   📊 Data Type: {market_data.data_type}")
            print(f"   💰 Bid: ${market_data.bid:.2f}")
            print(f"   💰 Ask: ${market_data.ask:.2f}")
            print(f"   💰 Mid: ${market_data.mid_price:.2f}")
            print(f"   💰 Last: ${market_data.last:.2f}")
            print(f"   📈 Volume: {market_data.volume:,}")
            print(f"   📊 Open Interest: {market_data.open_interest:,}")
            print(f"   📉 Implied Vol: {market_data.implied_volatility:.4f}")
            
            if market_data.bid > 0 or market_data.ask > 0 or market_data.last > 0:
                print("✅ SUCCESS: Real market data received!")
            else:
                print("⚠️  Market data structure created but values are zero (subscription issue)")
        else:
            print("❌ Failed to get market data")
        
        # Test a few more valid contracts
        print(f"\n🔄 Testing additional valid contracts...")
        
        test_contracts = [
            (588.0, '20250610'),
            (606.0, '20250611'),
            (620.0, '20250612')
        ]
        
        for strike, expiry in test_contracts:
            contract = OptionContract(
                symbol='SPY',
                expiry=expiry,
                strike=strike,
                right=OptionRight.CALL
            )
            
            market_data = client.get_option_market_data(contract)
            if market_data:
                print(f"   ✅ SPY ${strike} Call {expiry}: Bid=${market_data.bid:.2f}, Ask=${market_data.ask:.2f}")
            else:
                print(f"   ❌ SPY ${strike} Call {expiry}: Failed")
        
        print(f"\n🎯 CONCLUSION:")
        print(f"   • Valid contracts can be created and processed")
        print(f"   • Market data structure is working")
        print(f"   • Subscription errors are expected in paper trading")
        print(f"   • Use these exact values in the main application")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        client.disconnect()
        print("\n🔌 Disconnected from IB")

if __name__ == "__main__":
    test_valid_contract()
