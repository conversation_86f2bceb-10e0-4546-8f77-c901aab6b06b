#!/usr/bin/env python3
"""
Show valid contract values for SPY options
"""

from ib_options import IBOptionsClient
import time

def show_valid_contracts():
    print("🔍 FINDING VALID SPY OPTION CONTRACTS")
    print("=" * 60)
    
    # Create client and connect
    client = IBOptionsClient()
    try:
        print("🔌 Connecting to IB...")
        client.connect()
        time.sleep(2)
        
        if not client.is_connected():
            print("❌ Failed to connect to IB")
            return
        
        # Get SPY option chain
        print("📊 Fetching SPY option chain...")
        chain = client.get_option_chain('SPY')
        
        if not chain:
            print("❌ Failed to get SPY option chain")
            return
        
        print("✅ SPY Option Chain Retrieved")
        print(f"   Exchange: {chain.exchange}")
        print(f"   Total Expirations: {len(chain.expirations)}")
        print(f"   Total Strikes: {len(chain.strikes)}")
        
        if chain.underlying_price > 0:
            print(f"   Underlying Price: ${chain.underlying_price:.2f}")
        else:
            print(f"   Underlying Price: Not available (subscription required)")
        
        # Show available expirations
        print(f"\n📅 AVAILABLE EXPIRATIONS (First 10):")
        sorted_expirations = sorted(chain.expirations)[:10]
        for i, exp in enumerate(sorted_expirations, 1):
            print(f"   {i:2d}. {exp}")
        
        # Show available strikes in ranges
        sorted_strikes = sorted(chain.strikes)
        print(f"\n💰 AVAILABLE STRIKES:")
        print(f"   Total: {len(sorted_strikes)} strikes")
        print(f"   Range: ${sorted_strikes[0]:.0f} - ${sorted_strikes[-1]:.0f}")
        
        # Show strikes in groups around reasonable values
        print(f"\n📋 STRIKES AROUND CURRENT PRICE:")
        
        # Find strikes around 600 (typical SPY price)
        target_price = 600
        nearby_strikes = [s for s in sorted_strikes if abs(s - target_price) <= 50]
        
        if nearby_strikes:
            print(f"   Strikes near ${target_price} (±$50):")
            for i in range(0, len(nearby_strikes), 10):
                group = nearby_strikes[i:i+10]
                print(f"   {', '.join([f'${s:.0f}' for s in group])}")
        
        # Show some specific valid contracts
        print(f"\n✅ VALID CONTRACT EXAMPLES:")
        print(f"   Use these exact values in the application:")
        print(f"")
        
        if nearby_strikes and sorted_expirations:
            # Show 5 valid contracts
            sample_strikes = nearby_strikes[::max(1, len(nearby_strikes)//5)][:5]
            sample_expiry = sorted_expirations[0]  # Nearest expiry
            
            for i, strike in enumerate(sample_strikes, 1):
                print(f"   Example {i}:")
                print(f"     Symbol: SPY")
                print(f"     Call or Put: C")
                print(f"     Strike: {strike}")
                print(f"     Expiration: {sample_expiry}")
                print(f"")
        
        print(f"🎯 RECOMMENDED FOR TESTING:")
        if nearby_strikes and sorted_expirations:
            mid_strike = nearby_strikes[len(nearby_strikes)//2]
            print(f"   Symbol: SPY")
            print(f"   Call or Put: C")
            print(f"   Strike: {mid_strike}")
            print(f"   Expiration: {sorted_expirations[0]}")
        
        print(f"\n💡 TIPS:")
        print(f"   • Use strikes between ${nearby_strikes[0]:.0f} - ${nearby_strikes[-1]:.0f}")
        print(f"   • Use expiration: {sorted_expirations[0]} (nearest)")
        print(f"   • Market data may show subscription errors but contracts are valid")
        print(f"   • Orders can still be placed even without market data subscriptions")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        client.disconnect()
        print("\n🔌 Disconnected from IB")

if __name__ == "__main__":
    show_valid_contracts()
