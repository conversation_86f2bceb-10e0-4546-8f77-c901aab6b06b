# IB Client Portal

## Overview
This project provides a client portal for user authentication and interaction with the IB API. It includes functionalities for logging in and managing user sessions.

## Project Structure
```
ib-client-portal
├── src
│   ├── auth
│   │   ├── __init__.py
│   │   └── login.py
│   ├── api
│   │   ├── __init__.py
│   │   └── client.py
│   ├── utils
│   │   ├── __init__.py
│   │   └── logger.py
│   └── config.py
├── logs
│   └── .gitkeep
├── requirements.txt
├── .gitignore
└── README.md
```

## Installation
To install the required dependencies, run:
```
pip install -r requirements.txt
```

## Usage
To use the client portal, import the necessary modules and call the login function:
```python
from src.auth.login import login_user

# Example usage
session = login_user(username, password)
```

## Logging
Logs are stored in the `logs` directory. Ensure that the directory exists and is writable.

## Contributing
Feel free to submit issues or pull requests for improvements and bug fixes.

## License
This project is licensed under the MIT License.