#!/usr/bin/env python3
"""
Contract Validation Script for IB Options Trading POC

This script helps identify valid option contracts for testing.
It connects to IB and finds valid option contracts with proper expiry dates.
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from ib_options import IBOptionsClient, OptionContract, OptionRight, setup_logger

def main():
    """Main validation function"""
    logger = setup_logger('ContractValidator')
    
    print("="*60)
    print("IB Options Contract Validator")
    print("="*60)
    
    client = IBOptionsClient()
    
    try:
        # Connect to IB
        print("Connecting to Interactive Brokers...")
        if not client.connect():
            print("❌ Failed to connect to IB. Make sure TWS/Gateway is running on port 7497")
            return 1
        
        print("✅ Connected successfully!")
        
        # Test symbols
        symbols = ["SPY", "QQQ", "AAPL"]
        
        for symbol in symbols:
            print(f"\n{'='*20} {symbol} {'='*20}")
            
            # Get option chain
            print(f"Getting option chain for {symbol}...")
            chain = client.get_option_chain(symbol)
            
            if not chain:
                print(f"❌ No option chain found for {symbol}")
                continue
            
            print(f"✅ Option chain retrieved:")
            print(f"   Underlying Price: ${chain.underlying_price:.2f}")
            print(f"   Expirations: {len(chain.expirations)} available")
            print(f"   Strikes: {len(chain.strikes)} available")
            
            # Show first few expirations
            print(f"   First 5 expirations: {', '.join(chain.expirations[:5])}")
            
            # Find a valid contract
            print(f"\nFinding valid contract for {symbol}...")
            valid_contract = client.get_valid_option_contract(symbol, days_to_expiry=30)
            
            if valid_contract:
                print(f"✅ Valid contract found:")
                print(f"   {valid_contract.symbol} {valid_contract.right.value} ${valid_contract.strike} {valid_contract.expiry}")
                
                # Test market data for this contract
                print(f"   Testing market data...")
                market_data = client.get_option_market_data(valid_contract)
                
                if market_data:
                    print(f"   ✅ Market data retrieved:")
                    print(f"      Bid: ${market_data.bid:.2f}, Ask: ${market_data.ask:.2f}")
                    print(f"      Data Type: {market_data.data_type}")
                else:
                    print(f"   ⚠️  Market data not available (subscription issue)")
            else:
                print(f"❌ No valid contract found for {symbol}")
        
        print(f"\n{'='*60}")
        print("Contract validation complete!")
        print("Use the valid contracts found above for testing.")
        print("="*60)
        
        return 0
        
    except Exception as e:
        logger.error(f"Error in validation: {e}", exc_info=True)
        print(f"❌ Error: {e}")
        return 1
    finally:
        client.disconnect()
        print("Disconnected from IB")

if __name__ == "__main__":
    sys.exit(main())
