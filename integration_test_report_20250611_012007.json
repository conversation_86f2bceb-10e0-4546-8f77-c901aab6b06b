{"test_suite": "IB Options Trading POC Integration Test", "phase": "Phase 1: Python POC Development", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "timestamp": "2025-06-11T01:20:07.979264", "duration_seconds": 14.147858, "tests_passed": 10, "tests_total": 10, "success_rate": 100.0, "results": {"API Connection & Authentication": {"status": "PASS", "timestamp": "2025-06-11T01:19:53.979407"}, "Account Balances": {"status": "PASS", "timestamp": "2025-06-11T01:19:54.279241"}, "Open Options Positions": {"status": "PASS", "timestamp": "2025-06-11T01:19:54.519241"}, "Option Chains & Available Symbols": {"status": "PASS", "timestamp": "2025-06-11T01:19:56.614673"}, "Option Market Data": {"status": "PASS", "timestamp": "2025-06-11T01:20:00.938684"}, "Market Order Placement": {"status": "PASS", "timestamp": "2025-06-11T01:20:04.054546"}, "Limit Order Placement": {"status": "PASS", "timestamp": "2025-06-11T01:20:07.210196"}, "Order Status Tracking": {"status": "PASS", "timestamp": "2025-06-11T01:20:07.211264"}, "Error Handling & Logging": {"status": "PASS", "timestamp": "2025-06-11T01:20:07.977263"}, "Performance & Monitoring": {"status": "PASS", "timestamp": "2025-06-11T01:20:07.978264"}}, "configuration": {"ib_connection": {"host": "127.0.0.1", "port": 7497, "client_id": 1, "timeout": 30, "max_retries": 3, "retry_delay": 1.0}, "trading": {"default_exchange": "SMART", "default_currency": "USD", "max_order_quantity": 1000, "default_order_timeout": 300, "enable_paper_trading": true, "risk_check_enabled": true}, "market_data": {"use_delayed_data": true, "market_data_timeout": 10, "max_concurrent_requests": 50, "cache_duration": 60, "enable_greeks": true}, "logging": {"log_level": "INFO", "log_dir": "logs", "log_file_prefix": "ib_options", "max_log_files": 30, "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "enable_console_logging": true, "enable_file_logging": true}, "performance": {"enable_metrics": true, "metrics_interval": 60, "enable_benchmarking": false, "max_memory_usage_mb": 512, "enable_profiling": false}}}